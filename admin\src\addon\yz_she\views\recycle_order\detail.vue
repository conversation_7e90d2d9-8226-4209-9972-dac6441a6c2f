<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <span class="text-page-title">回收订单详情</span>
                </div>
                <div class="flex items-center space-x-2">
                    <el-button @click="refreshDetail()">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button @click="goBack()">返回</el-button>
                </div>
            </div>
        </el-card>

        <el-card class="box-card !border-none mt-[10px]" shadow="never" v-loading="loading">
            <div v-if="orderInfo.id">
                <!-- 订单基本信息 -->
                <div class="order-header mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold">{{ orderInfo.order_no }}</h3>
                            <p class="text-gray-500 mt-1">创建时间：{{ orderInfo.create_time_text }}</p>
                        </div>
                    </div>
                </div>

                <!-- 订单状态流程 -->
                <el-card class="mb-6" shadow="never">
                    <OrderStatusFlow :order-info="orderInfo" @action="handleStatusAction" />
                </el-card>

                <!-- 商品信息 -->
                <el-card class="mb-4" v-if="orderInfo.source_type == 1">
                    <template #header>
                        <div class="flex items-center space-x-2">
                            <el-icon class="text-blue-500"><Star /></el-icon>
                            <span>估价订单信息</span>
                        </div>
                    </template>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-4">
                            <img
                                v-if="orderInfo.product_image"
                                :src="img(orderInfo.product_image)"
                                class="w-20 h-20 rounded object-cover"
                                alt="商品图片"
                            />
                            <div v-else class="w-20 h-20 bg-blue-100 rounded flex items-center justify-center">
                                <el-icon class="text-blue-500"><Star /></el-icon>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold">{{ orderInfo.product_name || '估价商品' }}</h4>
                                <p class="text-gray-500">品牌：{{ orderInfo.brand?.name || '-' }}</p>
                                <p class="text-gray-500">分类：{{ orderInfo.category?.name || '-' }}</p>
                                <p class="text-gray-500">数量：{{ orderInfo.quantity }}</p>
                                <el-button type="primary" link size="small" @click="viewQuoteOrder(orderInfo.quote_order_id)" v-if="orderInfo.quote_order_id">
                                    查看估价详情
                                </el-button>
                            </div>
                        </div>
                        <!-- 估价订单照片预览 -->
                        <div v-if="quotePhotos.length > 0">
                            <h5 class="text-sm font-medium mb-2">用户上传照片</h5>
                            <div class="grid grid-cols-4 gap-2">
                                <img
                                    v-for="photo in quotePhotos.slice(0, 8)"
                                    :key="photo.id"
                                    :src="img(photo.photo_url)"
                                    class="w-16 h-16 rounded cursor-pointer object-cover"
                                    alt="估价照片"
                                    @click="previewImages(quotePhotos, photo)"
                                />
                                <div v-if="quotePhotos.length > 8" class="w-16 h-16 bg-gray-100 rounded flex items-center justify-center text-gray-500 text-xs">
                                    +{{ quotePhotos.length - 8 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>

                <!-- 直接回收商品信息 -->
                <el-card class="mb-4" v-else-if="orderInfo.source_type == 2">
                    <template #header>
                        <div class="flex items-center space-x-2">
                            <el-icon class="text-green-500"><Picture /></el-icon>
                            <span>直接回收商品</span>
                        </div>
                    </template>

                    <!-- 商品基本信息 -->
                    <div class="flex items-start space-x-4 mb-6">
                        <img
                            v-if="orderInfo.product_image"
                            :src="img(orderInfo.product_image)"
                            class="w-20 h-20 rounded object-cover"
                            alt="商品图片"
                        />
                        <div v-else class="w-20 h-20 bg-green-100 rounded flex items-center justify-center">
                            <el-icon class="text-green-500"><Picture /></el-icon>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-lg">{{ orderInfo.product_name }}</h4>
                            <div class="grid grid-cols-2 gap-2 mt-2">
                                <p class="text-gray-500 text-sm">编码：{{ orderInfo.product_code || '-' }}</p>
                                <p class="text-gray-500 text-sm">分类：{{ orderInfo.category?.name || '-' }}</p>
                                <p class="text-gray-500 text-sm">品牌：{{ orderInfo.brand?.name || '-' }}</p>
                                <p class="text-gray-500 text-sm">数量：{{ orderInfo.quantity }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 价格信息 -->
                    <div class="border-t pt-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-3">价格信息</h5>
                        <el-row :gutter="16">
                            <el-col :span="6">
                                <div class="text-center p-3 bg-blue-50 rounded-lg">
                                    <div class="text-xl font-bold text-blue-600">¥{{ orderInfo.expected_price }}</div>
                                    <div class="text-gray-500 text-sm">预期价格</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center p-3 bg-orange-50 rounded-lg">
                                    <div class="text-xl font-bold text-orange-600">+¥{{ orderInfo.voucher_amount || 0 }}</div>
                                    <div class="text-gray-500 text-sm">加价券</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center p-3 bg-green-50 rounded-lg">
                                    <div class="text-xl font-bold text-green-600">¥{{ orderInfo.final_price || '-' }}</div>
                                    <div class="text-gray-500 text-sm">最终价格</div>
                                </div>
                            </el-col>
                            <el-col :span="6">
                                <div class="text-center p-3 bg-red-50 rounded-lg">
                                    <div class="text-xl font-bold text-red-600">¥{{ orderInfo.total_amount || '-' }}</div>
                                    <div class="text-gray-500 text-sm">结算金额</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>

                <!-- 批量下单信息 -->
                <el-card class="mb-4" v-else-if="orderInfo.source_type == 3">
                    <template #header>
                        <div class="flex items-center space-x-2">
                            <el-icon class="text-orange-500"><Box /></el-icon>
                            <span>批量回收订单</span>
                        </div>
                    </template>
                    <div class="flex items-start space-x-4">
                        <div class="w-20 h-20 bg-orange-100 rounded flex items-center justify-center">
                            <span class="text-2xl font-bold text-orange-600">{{ orderInfo.quantity }}</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold">批量回收订单</h4>
                            <p class="text-gray-500">分类：{{ orderInfo.category?.name || '-' }}</p>
                            <p class="text-gray-500">数量：{{ orderInfo.quantity }}件</p>
                            <p class="text-orange-600">用户选择批量回收，具体商品信息待现场确认</p>
                        </div>
                    </div>
                </el-card>


                <!-- 订单详情 -->
                <el-card class="mb-4" header="订单详情">
                    <el-descriptions :column="3" border>
                        <el-descriptions-item label="订单编号">
                            {{ orderInfo.order_no }}
                        </el-descriptions-item>
                        <el-descriptions-item label="订单状态">
                            <el-tag :type="getStatusTagType(orderInfo.status)">
                                {{ orderInfo.status_text }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="订单来源">
                            <el-tag :type="getSourceTypeTagType(orderInfo.source_type)">
                                {{ orderInfo.source_type_text }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="创建时间">
                            {{ orderInfo.create_time_text }}
                        </el-descriptions-item>
                        <el-descriptions-item label="更新时间">
                            {{ orderInfo.update_time_text }}
                        </el-descriptions-item>
                        <el-descriptions-item label="结算状态">
                            <el-tag v-if="orderInfo.settlement_status == 1" type="success">已结算</el-tag>
                            <el-tag v-else type="info">未结算</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="用户信息" span="3">
                            <div class="flex items-center space-x-4">
                                <el-avatar :src="orderInfo.member?.headimg" :size="40">
                                    {{ orderInfo.member?.nickname?.charAt(0) }}
                                </el-avatar>
                                <div>
                                    <p class="font-medium">{{ orderInfo.member?.nickname || '-' }}</p>
                                    <p class="text-gray-500 text-sm">手机：{{ orderInfo.member?.mobile || '-' }}</p>
                                </div>
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- 价格信息 -->
                <el-card class="mb-4" header="价格信息">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">¥{{ orderInfo.expected_price }}</div>
                                <div class="text-gray-500">预期价格</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-warning">+¥{{ orderInfo.voucher_amount || 0 }}</div>
                                <div class="text-gray-500">加价券</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-success">¥{{ orderInfo.final_price || '-' }}</div>
                                <div class="text-gray-500">最终价格</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-danger">¥{{ orderInfo.total_amount || '-' }}</div>
                                <div class="text-gray-500">结算金额</div>
                            </div>
                        </el-col>
                    </el-row>
                </el-card>

                <!-- 配送信息 -->
                <el-card class="mb-4" header="配送信息">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="配送方式">
                            <el-tag :type="orderInfo.delivery_type == 1 ? 'primary' : 'success'">
                                {{ orderInfo.delivery_type_text }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="快递费用" v-if="orderInfo.express_fee > 0">
                            ¥{{ orderInfo.express_fee }}
                        </el-descriptions-item>

                        <!-- 快递上门信息 -->
                        <template v-if="orderInfo.delivery_type == 1">
                            <el-descriptions-item label="联系人">
                                {{ orderInfo.pickup_contact_name || '-' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="联系电话">
                                {{ orderInfo.pickup_contact_phone || '-' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="取件地址" span="2">
                                <div class="space-y-1">
                                    <p class="font-medium">{{ orderInfo.pickup_address_detail || orderInfo.pickup_address?.full_address || '-' }}</p>
                                    <p class="text-gray-500 text-sm" v-if="orderInfo.pickup_address">
                                        {{ orderInfo.pickup_address.province_name }}
                                        {{ orderInfo.pickup_address.city_name }}
                                        {{ orderInfo.pickup_address.district_name }}
                                    </p>
                                </div>
                            </el-descriptions-item>
                            <el-descriptions-item label="期望时间">
                                {{ orderInfo.pickup_time || '-' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="实际取件时间" v-if="orderInfo.pickup_time_actual_text">
                                {{ orderInfo.pickup_time_actual_text }}
                            </el-descriptions-item>
                        </template>

                        <!-- 自行寄出信息 -->
                        <template v-if="orderInfo.delivery_type == 2">
                            <el-descriptions-item label="快递公司">
                                {{ orderInfo.express_company || '-' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="快递单号">
                                <div class="flex items-center space-x-2">
                                    <span>{{ orderInfo.express_number || '-' }}</span>
                                    <el-button v-if="orderInfo.express_number" type="primary" link size="small" @click="copyText(orderInfo.express_number)">
                                        复制
                                    </el-button>
                                </div>
                            </el-descriptions-item>
                        </template>
                    </el-descriptions>
                </el-card>

                <!-- 质检信息 -->
                <el-card class="mb-4" header="质检信息" v-if="orderInfo.quality_score">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="质检评分">
                            <el-rate v-model="qualityRate" disabled show-score />
                            <span class="ml-2">{{ orderInfo.quality_score }}分</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="质检员">
                            {{ orderInfo.quality_admin?.real_name || orderInfo.quality_admin?.username || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="质检说明" span="2">
                            {{ orderInfo.quality_note || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="质检图片" span="2" v-if="orderInfo.quality_images?.length">
                            <div class="flex flex-wrap gap-2">
                                <img
                                    v-for="(image, index) in orderInfo.quality_images"
                                    :key="index"
                                    :src="img(image)"
                                    class="w-20 h-20 rounded cursor-pointer object-cover"
                                    alt="质检图片"
                                    @click="previewQualityImages(orderInfo.quality_images, index)"
                                />
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>





                <!-- 备注信息 -->
                <el-card class="mb-4" header="备注信息">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="用户备注">
                            {{ orderInfo.user_note || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="管理员备注">
                            {{ orderInfo.admin_note || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="拒绝原因" v-if="orderInfo.reject_reason">
                            {{ orderInfo.reject_reason }}
                        </el-descriptions-item>
                        <el-descriptions-item label="退回原因" v-if="orderInfo.return_reason">
                            {{ orderInfo.return_reason }}
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- 操作按钮 -->
                <div class="text-center">
                    <el-button v-if="orderInfo.status == 2" type="primary" @click="receiveOrder()">
                        收货确认
                    </el-button>
                    <el-button v-if="orderInfo.status == 3" type="primary" @click="startQuality()">
                        开始质检
                    </el-button>
                    <el-button v-if="orderInfo.status == 4" type="primary" @click="completeQuality()">
                        完成质检
                    </el-button>
                    <el-button v-if="orderInfo.status == 7 && orderInfo.settlement_status == 0" type="success" @click="settlement()">
                        结算订单
                    </el-button>
                    <el-button v-if="orderInfo.status == 5" type="warning" @click="returnOrder()">
                        确认退回
                    </el-button>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Star, Picture, Box, CopyDocument } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import {
    getRecycleOrderInfo,
    receiveRecycleOrder,
    startQualityCheck,
    completeQualityCheck,
    settlementRecycleOrder,
    returnRecycleOrder
} from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'
import OrderStatusFlow from './components/OrderStatusFlow.vue'


const route = useRoute()
const router = useRouter()

const loading = ref(true)
const orderInfo = ref<any>({})
const quotePhotos = ref<any[]>([]) // 估价订单照片

// 质检评分（转换为5星制）
const qualityRate = computed(() => {
    return orderInfo.value.quality_score ? Math.round(orderInfo.value.quality_score / 20) : 0
})



/**
 * 获取订单详情
 */
const loadOrderInfo = () => {
    loading.value = true
    const id = route.params.id as string

    getRecycleOrderInfo(parseInt(id)).then(res => {
        loading.value = false
        orderInfo.value = res.data
    }).catch(() => {
        loading.value = false
    })
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: number) => {
    const tagTypes: Record<number, string> = {
        1: 'warning',  // 待取件
        2: 'primary',  // 待收货
        3: 'info',     // 待质检
        4: 'warning',  // 待确认
        5: 'danger',   // 待退回
        6: 'info',     // 已退回
        7: 'success'   // 已完成
    }
    return tagTypes[status] || 'info'
}

/**
 * 获取订单来源标签类型
 */
const getSourceTypeTagType = (sourceType: number) => {
    const tagTypes: Record<number, string> = {
        1: 'primary',   // 估价订单确认
        2: 'success',   // 直接回收
        3: 'warning',   // 批量下单
        4: 'info'       // 批量估价下单
    }
    return tagTypes[sourceType] || 'info'
}

/**
 * 刷新详情
 */
const refreshDetail = () => {
    loadOrderInfo()
    if (orderInfo.value.quote_order_id) {
        loadQuotePhotos()
    }
}

/**
 * 复制文本
 */
const copyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

/**
 * 查看估价订单
 */
const viewQuoteOrder = (quoteOrderId: number) => {
    router.push(`/yz_she/quote-order/detail/${quoteOrderId}`)
}



/**
 * 加载估价订单照片
 */
const loadQuotePhotos = () => {
    if (!orderInfo.value.quote_order_id) return

    // 这里应该调用获取估价订单照片的API
    // getQuoteOrderPhotos(orderInfo.value.quote_order_id).then(res => {
    //     quotePhotos.value = res.data
    // })
}

/**
 * 预览估价图片
 */
const previewImages = (photos: any[], currentPhoto: any) => {
    const imageUrls = photos.map(photo => img(photo.photo_url))
    const currentIndex = photos.findIndex(photo => photo.id === currentPhoto.id)

    // 这里可以使用 Element Plus 的图片预览功能
    // 或者自定义图片预览组件
    console.log('预览图片', imageUrls, currentIndex)
}

/**
 * 预览质检图片
 */
const previewQualityImages = (images: string[], currentIndex: number) => {
    const imageUrls = images.map(image => img(image))

    // 这里可以使用 Element Plus 的图片预览功能
    // 或者自定义图片预览组件
    console.log('预览质检图片', imageUrls, currentIndex)
}



/**
 * 返回列表
 */
const goBack = () => {
    router.go(-1)
}

/**
 * 收货确认
 */
const receiveOrder = () => {
    ElMessageBox.prompt('请输入收货备注', '收货确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入收货备注（可选）'
    }).then(({ value }) => {
        const params = {
            receive_note: value || '',
            admin_id: 1 // 这里应该从用户信息中获取
        }
        receiveRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('收货成功')
            loadOrderInfo()
        })
    })
}

/**
 * 开始质检
 */
const startQuality = () => {
    ElMessageBox.confirm('确认开始质检该订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1 // 这里应该从用户信息中获取
        }
        startQualityCheck(orderInfo.value.id, params).then(() => {
            ElMessage.success('开始质检成功')
            loadOrderInfo()
        })
    })
}

/**
 * 完成质检
 */
const completeQuality = () => {
    router.push(`/yz_she/recycle_order/quality/${orderInfo.value.id}`)
}

/**
 * 结算订单
 */
const settlement = () => {
    ElMessageBox.confirm('确认结算该订单？结算后金额将直接转入用户余额。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            settlement_note: '订单结算完成'
        }
        settlementRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('结算成功')
            loadOrderInfo()
        })
    })
}

/**
 * 退回订单
 */
const returnOrder = () => {
    ElMessageBox.prompt('请输入退回原因', '确认退回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '退回原因不能为空'
    }).then(({ value }) => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            return_note: value
        }
        returnRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('退回成功')
            loadOrderInfo()
        })
    })
}

/**
 * 处理状态操作
 */
const handleStatusAction = (actionKey: string, orderInfo: any) => {
    switch (actionKey) {
        case 'update_express':
            // 更新快递信息
            console.log('更新快递信息', orderInfo)
            break
        case 'receive_order':
            receiveOrder()
            break
        case 'start_quality':
            startQuality()
            break
        case 'complete_quality':
            completeQuality()
            break
        case 'settlement':
            settlement()
            break
        default:
            console.log('未知操作:', actionKey)
    }
}

onMounted(() => {
    loadOrderInfo()
})
</script>

<style lang="scss" scoped>
.text-primary {
    color: #409eff;
}

.text-success {
    color: #67c23a;
}

.text-warning {
    color: #e6a23c;
}

.text-danger {
    color: #f56c6c;
}

.order-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 1rem;
}
</style>
