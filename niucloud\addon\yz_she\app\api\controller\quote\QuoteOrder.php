<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\api\controller\quote;

use core\base\BaseApiController;
use addon\yz_she\app\service\api\quote\QuoteOrderService;

/**
 * 估价订单控制器
 * Class QuoteOrder
 * @package addon\yz_she\app\api\controller\quote
 */
class QuoteOrder extends BaseApiController
{
    /**
     * 创建估价订单
     * @return \think\Response
     */
    public function create()
    {
        $data = $this->request->params([
            ['category_id', 0],
            ['brand_id', 0],
            ['product_id', 0],
            ['product_name', ''],
            ['product_code', ''],
            ['product_image', ''],
            ['photos', []],
            ['defect_photos', []],
            ['accessories', []],
            ['note', '']
        ]);

        try {
            $result = (new QuoteOrderService())->create($data);
            return success('SUCCESS', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 获取估价订单列表
     * @return \think\Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['order_no', ''],
            ['status', ''],
            ['category_id', ''],
            ['brand_id', ''],
            ['page', 1],
            ['limit', 10]
        ]);

        return success('SUCCESS', (new QuoteOrderService())->getPage($data));
    }

    /**
     * 获取估价订单详情
     * @param int $id
     * @return \think\Response
     */
    public function detail(int $id)
    {
        try {
            $result = (new QuoteOrderService())->getDetail($id);
            return success('SUCCESS', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 确认估价
     * @param int $id
     * @return \think\Response
     */
    public function confirm(int $id)
    {
        try {
            (new QuoteOrderService())->confirm($id);
            return success('确认成功');
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 取消订单
     * @param int $id
     * @return \think\Response
     */
    public function cancel(int $id)
    {
        $data = $this->request->params([
            ['reason', '']
        ]);

        try {
            (new QuoteOrderService())->cancel($id, $data['reason']);
            return success('取消成功');
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 更新订单状态
     * @param int $id
     * @return \think\Response
     */
    public function updateStatus(int $id)
    {
        $data = $this->request->params([
            ['status', 0]
        ]);

        try {
            (new QuoteOrderService())->updateStatus($id, $data['status']);
            return success('状态更新成功');
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 批量获取订单详情
     * @return \think\Response
     */
    public function batchDetail()
    {
        $data = $this->request->params([
            ['order_ids', []]
        ]);

        try {
            $result = (new QuoteOrderService())->getBatchOrderDetails($data['order_ids']);
            return success('获取成功', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 批量发货
     * @return \think\Response
     */
    public function batchShip()
    {
        $data = $this->request->params([
            ['order_ids', []], // 订单ID数组
            ['delivery_type', 1], // 配送方式：1=快递上门，2=用户自寄
            ['pickup_address_id', 0], // 取件地址ID（快递上门时必填）
            ['pickup_time', ''], // 期望上门时间
            ['express_company', ''], // 快递公司（用户自寄时必填）
            ['express_number', ''], // 快递单号（用户自寄时必填）
            ['ship_note', ''] // 发货备注
        ]);

        try {
            $result = (new QuoteOrderService())->batchShip($data);
            return success('批量发货成功', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 获取订单状态列表
     * @return \think\Response
     */
    public function getStatus()
    {
        $statusList = [
            ['value' => 1, 'label' => '估价中'],
            ['value' => 2, 'label' => '待确认'],
            ['value' => 3, 'label' => '待发货'],
            ['value' => 4, 'label' => '已完成'],
            ['value' => 5, 'label' => '已取消']
        ];

        return success('SUCCESS', $statusList);
    }

    /**
     * 获取购物车数量（待发货状态的估价订单数量）
     * @return \think\Response
     */
    public function getCartCount()
    {
        try {
            $result = (new QuoteOrderService())->getCartCount();
            return success('SUCCESS', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }

    /**
     * 添加到购物车（创建待发货状态的估价订单）
     * @return \think\Response
     */
    public function addToCart()
    {
        $data = $this->request->params([
            ['category_id', 0],
            ['brand_id', 0],
            ['product_id', 0],
            ['product_name', ''],
            ['product_code', ''],
            ['product_image', ''],
            ['quote_price', 0],
            ['condition_type', ''],
            ['voucher_id', 0],
            ['voucher_amount', 0],
            ['note', '']
        ]);

        try {
            $result = (new QuoteOrderService())->addToCart($data);
            return success('SUCCESS', $result);
        } catch (\Exception $e) {
            return fail($e->getMessage());
        }
    }
}
