<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\api\quote;

use addon\yz_she\app\model\quote\QuoteOrder;
use addon\yz_she\app\model\quote\QuotePhoto;
use addon\yz_she\app\model\quote\QuoteAccessory;
use core\base\BaseApiService;
use think\facade\Db;

/**
 * 估价订单服务类
 * Class QuoteOrderService
 * @package addon\yz_she\app\service\api\quote
 */
class QuoteOrderService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new QuoteOrder();
    }

    /**
     * 创建估价订单
     * @param array $data
     * @return array
     */
    public function create(array $data)
    {
        // 验证必要参数
        if (empty($data['category_id'])) {
            throw new \Exception('分类ID不能为空');
        }

        if (empty($data['photos'])) {
            throw new \Exception('请至少上传一张照片');
        }

        Db::startTrans();
        try {
            // 生成订单编号
            $orderNo = $this->generateOrderNo();

            // 创建主订单
            $orderData = [
                'order_no' => $orderNo,
                'user_id' => $this->member_id,
                'category_id' => $data['category_id'],
                'brand_id' => $data['brand_id'] ?? null,
                'product_id' => $data['product_id'] ?? null,
                'product_name' => $data['product_name'] ?? '',
                'product_code' => $data['product_code'] ?? '',
                'product_image' => $data['product_image'] ?? '',
                'status' => 1, // 估价中
                'user_note' => $data['note'] ?? '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $order = $this->model->create($orderData);
            $orderId = $order->id;

            // 保存照片
            $this->savePhotos($orderId, $data['photos'], $data['defect_photos'] ?? []);

            // 保存配件
            if (!empty($data['accessories'])) {
                $this->saveAccessories($orderId, $data['accessories']);
            }

            Db::commit();

            return [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'message' => '估价订单创建成功'
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('创建估价订单失败：' . $e->getMessage());
        }
    }

    /**
     * 获取估价订单列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,order_no,user_id,category_id,brand_id,product_id,product_name,product_code,product_image,status,quote_price,user_note,admin_note,quote_time,create_time';
        $order = 'create_time desc';

        $search_model = $this->model->where([['user_id', '=', $this->member_id]])
            ->withSearch(['order_no', 'status', 'category_id', 'brand_id'], $where)
            ->field($field)
            ->order($order)
            ->with(['category', 'brand']);

        $list = $this->pageQuery($search_model);
        return $list;
    }

    /**
     * 获取估价订单详情
     * @param int $id
     * @return array
     */
    public function getDetail(int $id)
    {
        $field = 'id,order_no,user_id,category_id,brand_id,product_id,product_name,product_code,product_image,status,quote_price,user_note,admin_note,admin_id,quote_time,confirm_time,ship_time,complete_time,cancel_time,cancel_reason,create_time,update_time';
        
        $info = $this->model->field($field)
            ->where([
                ['id', '=', $id],
                ['user_id', '=', $this->member_id]
            ])
            ->with(['category', 'brand', 'photos', 'accessories', 'latestQuoteRecord'])
            ->findOrEmpty()
            ->toArray();

        if (empty($info)) {
            throw new \Exception('估价订单不存在');
        }

        return $info;
    }

    /**
     * 确认估价
     * @param int $id
     * @return bool
     */
    public function confirm(int $id)
    {
        $order = $this->model->where([
            ['id', '=', $id],
            ['user_id', '=', $this->member_id],
            ['status', '=', 2] // 待确认状态
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在或状态不正确');
        }

        $order->status = 3; // 待发货
        $order->confirm_time = date('Y-m-d H:i:s');
        $order->auto_cancel_time = date('Y-m-d H:i:s', time() + 48 * 3600); // 48小时后自动取消
        $order->save();

        return true;
    }

    /**
     * 取消订单
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function cancel(int $id, string $reason = '')
    {
        $order = $this->model->where([
            ['id', '=', $id],
            ['user_id', '=', $this->member_id],
            ['status', 'in', [1, 2, 3]] // 估价中、待确认、待发货状态可以取消
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在或状态不正确');
        }

        $order->status = 5; // 已取消
        $order->cancel_time = date('Y-m-d H:i:s');
        $order->cancel_reason = $reason;
        $order->save();

        return true;
    }

    /**
     * 生成订单编号
     * @return string
     */
    private function generateOrderNo()
    {
        return 'YZ' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 保存照片
     * @param int $orderId
     * @param array $photos
     * @param array $defectPhotos
     */
    private function savePhotos(int $orderId, array $photos, array $defectPhotos = [])
    {
        $photoModel = new QuotePhoto();
        $photoData = [];

        // 保存普通照片
        foreach ($photos as $configId => $photoUrl) {
            if (!empty($photoUrl)) {
                $photoData[] = [
                    'quote_order_id' => $orderId,
                    'photo_config_id' => $configId,
                    'photo_type' => 'normal',
                    'photo_name' => '商品照片',
                    'photo_url' => $photoUrl,
                    'is_defect' => 0,
                    'sort' => 0,
                    'create_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        // 保存瑕疵照片
        foreach ($defectPhotos as $index => $photoUrl) {
            if (!empty($photoUrl)) {
                $photoData[] = [
                    'quote_order_id' => $orderId,
                    'photo_config_id' => null,
                    'photo_type' => 'defect',
                    'photo_name' => '瑕疵照片',
                    'photo_url' => $photoUrl,
                    'is_defect' => 1,
                    'sort' => $index,
                    'create_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        if (!empty($photoData)) {
            $photoModel->insertAll($photoData);
        }
    }

    /**
     * 保存配件
     * @param int $orderId
     * @param array $accessories
     */
    private function saveAccessories(int $orderId, array $accessories)
    {
        $accessoryModel = new QuoteAccessory();
        $accessoryData = [];

        foreach ($accessories as $accessory) {
            $accessoryData[] = [
                'quote_order_id' => $orderId,
                'accessory_config_id' => $accessory['id'] ?? null,
                'accessory_name' => $accessory['accessory_name'] ?? $accessory['name'] ?? '',
                'create_time' => date('Y-m-d H:i:s')
            ];
        }

        if (!empty($accessoryData)) {
            $accessoryModel->insertAll($accessoryData);
        }
    }
}
