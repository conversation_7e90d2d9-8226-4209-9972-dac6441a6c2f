<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\api\quote;

use addon\yz_she\app\model\quote\QuoteOrder;
use addon\yz_she\app\model\quote\QuotePhoto;
use addon\yz_she\app\model\quote\QuoteAccessory;
use core\base\BaseApiService;
use think\facade\Db;

/**
 * 估价订单服务类
 * Class QuoteOrderService
 * @package addon\yz_she\app\service\api\quote
 */
class QuoteOrderService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new QuoteOrder();
    }

    /**
     * 创建估价订单
     * @param array $data
     * @return array
     */
    public function create(array $data)
    {
        // 验证必要参数
        if (empty($data['category_id'])) {
            throw new \Exception('分类ID不能为空');
        }

        if (empty($data['photos'])) {
            throw new \Exception('请至少上传一张照片');
        }

        Db::startTrans();
        try {
            // 生成订单编号
            $orderNo = $this->generateOrderNo();

            // 创建主订单
            $orderData = [
                'order_no' => $orderNo,
                'user_id' => $this->member_id,
                'category_id' => $data['category_id'],
                'brand_id' => $data['brand_id'] ?? null,
                'product_id' => $data['product_id'] ?? null,
                'product_name' => $data['product_name'] ?? '',
                'product_code' => $data['product_code'] ?? '',
                'product_image' => $data['product_image'] ?? '',
                'status' => 1, // 估价中
                'user_note' => $data['note'] ?? '',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $order = $this->model->create($orderData);
            $orderId = $order->id;

            // 保存照片
            $this->savePhotos($orderId, $data['photos'], $data['defect_photos'] ?? []);

            // 保存配件
            if (!empty($data['accessories'])) {
                $this->saveAccessories($orderId, $data['accessories']);
            }

            Db::commit();

            return [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'message' => '估价订单创建成功'
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('创建估价订单失败：' . $e->getMessage());
        }
    }

    /**
     * 获取估价订单列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,order_no,user_id,category_id,brand_id,product_id,product_name,product_code,product_image,status,quote_price,user_note,admin_note,quote_time,create_time';
        $order = 'create_time desc';

        $search_model = $this->model->where([['user_id', '=', $this->member_id]])
            ->withSearch(['order_no', 'status', 'category_id', 'brand_id'], $where)
            ->field($field)
            ->order($order)
            ->with(['category', 'brand']);

        $list = $this->pageQuery($search_model);
        return $list;
    }

    /**
     * 获取估价订单详情
     * @param int $id
     * @return array
     */
    public function getDetail(int $id)
    {
        $field = 'id,order_no,user_id,category_id,brand_id,product_id,product_name,product_code,product_image,status,quote_price,user_note,admin_note,admin_id,quote_time,confirm_time,ship_time,complete_time,cancel_time,cancel_reason,create_time,update_time';
        
        $info = $this->model->field($field)
            ->where([
                ['id', '=', $id],
                ['user_id', '=', $this->member_id]
            ])
            ->with(['category', 'brand', 'photos', 'accessories', 'latestQuoteRecord'])
            ->findOrEmpty()
            ->toArray();

        if (empty($info)) {
            throw new \Exception('估价订单不存在');
        }

        return $info;
    }

    /**
     * 确认估价
     * @param int $id
     * @return bool
     */
    public function confirm(int $id)
    {
        $order = $this->model->where([
            ['id', '=', $id],
            ['user_id', '=', $this->member_id],
            ['status', '=', 2] // 待确认状态
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在或状态不正确');
        }

        $order->status = 3; // 待发货
        $order->confirm_time = date('Y-m-d H:i:s');
        $order->auto_cancel_time = date('Y-m-d H:i:s', time() + 48 * 3600); // 48小时后自动取消
        $order->save();

        return true;
    }

    /**
     * 取消订单
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function cancel(int $id, string $reason = '')
    {
        $order = $this->model->where([
            ['id', '=', $id],
            ['user_id', '=', $this->member_id],
            ['status', 'in', [1, 2, 3]] // 估价中、待确认、待发货状态可以取消
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在或状态不正确');
        }

        $order->status = 5; // 已取消
        $order->cancel_time = date('Y-m-d H:i:s');
        $order->cancel_reason = $reason;
        $order->save();

        return true;
    }

    /**
     * 更新订单状态
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function updateStatus(int $id, int $status)
    {
        // 验证状态值
        $allowedStatus = [1, 2, 3, 4, 5]; // 估价中、待确认、待发货、已完成、已取消
        if (!in_array($status, $allowedStatus)) {
            throw new \Exception('无效的状态值');
        }

        $order = $this->model->where([
            ['id', '=', $id],
            ['user_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        // 验证状态转换是否合法
        $this->validateStatusTransition($order->status, $status);

        // 更新状态
        $updateData = ['status' => $status];

        // 根据状态设置相应的时间字段
        switch ($status) {
            case 2: // 待确认
                $updateData['quote_time'] = date('Y-m-d H:i:s');
                break;
            case 3: // 待发货
                $updateData['confirm_time'] = date('Y-m-d H:i:s');
                $updateData['auto_cancel_time'] = date('Y-m-d H:i:s', time() + 48 * 3600); // 48小时后自动取消
                break;
            case 4: // 已完成
                $updateData['complete_time'] = date('Y-m-d H:i:s');
                break;
            case 5: // 已取消
                $updateData['cancel_time'] = date('Y-m-d H:i:s');
                break;
        }

        $updateData['update_time'] = date('Y-m-d H:i:s');

        $order->save($updateData);

        return true;
    }

    /**
     * 验证状态转换是否合法
     * @param int $currentStatus
     * @param int $newStatus
     * @throws \Exception
     */
    private function validateStatusTransition(int $currentStatus, int $newStatus)
    {
        // 定义合法的状态转换规则
        $allowedTransitions = [
            1 => [2, 5], // 估价中 -> 待确认、已取消
            2 => [3, 5], // 待确认 -> 待发货、已取消
            3 => [4, 5], // 待发货 -> 已完成、已取消
            4 => [],     // 已完成 -> 无法转换
            5 => []      // 已取消 -> 无法转换
        ];

        if (!isset($allowedTransitions[$currentStatus]) || !in_array($newStatus, $allowedTransitions[$currentStatus])) {
            $statusNames = [
                1 => '估价中',
                2 => '待确认',
                3 => '待发货',
                4 => '已完成',
                5 => '已取消'
            ];

            throw new \Exception(sprintf(
                '无法从状态"%s"转换到"%s"',
                $statusNames[$currentStatus] ?? $currentStatus,
                $statusNames[$newStatus] ?? $newStatus
            ));
        }
    }

    /**
     * 生成订单编号
     * @return string
     */
    private function generateOrderNo()
    {
        return 'YZ' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 保存照片
     * @param int $orderId
     * @param array $photos
     * @param array $defectPhotos
     */
    private function savePhotos(int $orderId, array $photos, array $defectPhotos = [])
    {
        $photoModel = new QuotePhoto();
        $photoData = [];

        // 保存普通照片
        foreach ($photos as $configId => $photoUrl) {
            if (!empty($photoUrl)) {
                $photoData[] = [
                    'quote_order_id' => $orderId,
                    'photo_config_id' => $configId,
                    'photo_type' => 'normal',
                    'photo_name' => '商品照片',
                    'photo_url' => $photoUrl,
                    'is_defect' => 0,
                    'sort' => 0,
                    'create_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        // 保存瑕疵照片
        foreach ($defectPhotos as $index => $photoUrl) {
            if (!empty($photoUrl)) {
                $photoData[] = [
                    'quote_order_id' => $orderId,
                    'photo_config_id' => null,
                    'photo_type' => 'defect',
                    'photo_name' => '瑕疵照片',
                    'photo_url' => $photoUrl,
                    'is_defect' => 1,
                    'sort' => $index,
                    'create_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        if (!empty($photoData)) {
            $photoModel->insertAll($photoData);
        }
    }

    /**
     * 保存配件
     * @param int $orderId
     * @param array $accessories
     */
    private function saveAccessories(int $orderId, array $accessories)
    {
        $accessoryModel = new QuoteAccessory();
        $accessoryData = [];

        foreach ($accessories as $accessory) {
            $accessoryData[] = [
                'quote_order_id' => $orderId,
                'accessory_config_id' => $accessory['id'] ?? null,
                'accessory_name' => $accessory['accessory_name'] ?? $accessory['name'] ?? '',
                'create_time' => date('Y-m-d H:i:s')
            ];
        }

        if (!empty($accessoryData)) {
            $accessoryModel->insertAll($accessoryData);
        }
    }

    /**
     * 批量获取订单详情
     * @param array $orderIds
     * @return array
     */
    public function getBatchOrderDetails(array $orderIds)
    {
        if (empty($orderIds)) {
            throw new \Exception('订单ID不能为空');
        }

        $orders = $this->model->where([
            ['id', 'in', $orderIds],
            ['user_id', '=', $this->member_id],
            ['status', '=', 3] // 只能批量发货待发货状态的订单
        ])->with([
            'category',
            'brand',
            'product'
        ])->select()->toArray();

        if (empty($orders)) {
            throw new \Exception('没有找到可发货的订单');
        }

        if (count($orders) !== count($orderIds)) {
            throw new \Exception('部分订单不存在或状态不正确');
        }

        return $orders;
    }

    /**
     * 批量发货
     * @param array $data
     * @return array
     */
    public function batchShip(array $data)
    {
        if (empty($data['order_ids'])) {
            throw new \Exception('订单ID不能为空');
        }

        // 验证订单状态
        $orders = $this->model->where([
            ['id', 'in', $data['order_ids']],
            ['user_id', '=', $this->member_id],
            ['status', '=', 3] // 只能发货待发货状态的订单
        ])->select();

        if ($orders->isEmpty()) {
            throw new \Exception('没有找到可发货的订单');
        }

        if (count($orders) !== count($data['order_ids'])) {
            throw new \Exception('部分订单不存在或状态不正确');
        }

        // 验证发货参数
        $this->validateShipData($data);

        // 开启事务
        $this->model->startTrans();
        try {
            $successCount = 0;
            $failedOrders = [];

            foreach ($orders as $order) {
                try {
                    // 更新订单状态为已发货
                    $updateData = [
                        'status' => 4, // 已完成
                        'ship_time' => date('Y-m-d H:i:s'),
                        'complete_time' => date('Y-m-d H:i:s'),
                        'update_time' => date('Y-m-d H:i:s')
                    ];

                    // 根据配送方式设置不同字段
                    if ($data['delivery_type'] == 1) {
                        // 快递上门
                        $updateData['pickup_address_id'] = $data['pickup_address_id'];
                        $updateData['pickup_time'] = $data['pickup_time'];
                        $updateData['admin_note'] = '快递上门取件，预约时间：' . $data['pickup_time'];
                    } else {
                        // 用户自寄
                        $updateData['express_company'] = $data['express_company'];
                        $updateData['express_number'] = $data['express_number'];
                        $updateData['admin_note'] = '用户自寄，快递公司：' . $data['express_company'] . '，快递单号：' . $data['express_number'];
                    }

                    if (!empty($data['ship_note'])) {
                        $updateData['admin_note'] .= '，备注：' . $data['ship_note'];
                    }

                    $order->save($updateData);
                    $successCount++;

                } catch (\Exception $e) {
                    $failedOrders[] = [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'error' => $e->getMessage()
                    ];
                }
            }

            if ($successCount === 0) {
                throw new \Exception('批量发货失败');
            }

            $this->model->commit();

            return [
                'success_count' => $successCount,
                'total_count' => count($orders),
                'failed_orders' => $failedOrders
            ];

        } catch (\Exception $e) {
            $this->model->rollback();
            throw $e;
        }
    }

    /**
     * 验证发货数据
     * @param array $data
     * @throws \Exception
     */
    private function validateShipData(array $data)
    {
        if (!in_array($data['delivery_type'], [1, 2])) {
            throw new \Exception('配送方式无效');
        }

        if ($data['delivery_type'] == 1) {
            // 快递上门验证
            if (empty($data['pickup_address_id'])) {
                throw new \Exception('快递上门需要选择取件地址');
            }
        } else {
            // 用户自寄验证
            if (empty($data['express_company'])) {
                throw new \Exception('用户自寄需要选择快递公司');
            }
            if (empty($data['express_number'])) {
                throw new \Exception('用户自寄需要填写快递单号');
            }
        }
    }
}
