<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\quote;

use addon\yz_she\app\model\config\Category;
use addon\yz_she\app\model\goods\Brand;
use addon\yz_she\app\model\goods\Goods;
use app\model\member\Member;
use core\base\BaseModel;

/**
 * 估价订单模型
 * Class QuoteOrder
 * @package addon\yz_she\app\model\quote
 */
class QuoteOrder extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_quote_orders';

    /**
     * 关联分类
     * @return \think\model\relation\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 关联品牌
     * @return \think\model\relation\BelongsTo
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class, 'brand_id', 'id');
    }

    /**
     * 关联商品
     * @return \think\model\relation\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo(Goods::class, 'product_id', 'id');
    }

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function member()
    {
        return $this->belongsTo(Member::class, 'user_id', 'member_id');
    }

    /**
     * 关联照片
     * @return \think\model\relation\HasMany
     */
    public function photos()
    {
        return $this->hasMany(QuotePhoto::class, 'quote_order_id', 'id');
    }

    /**
     * 关联配件
     * @return \think\model\relation\HasMany
     */
    public function accessories()
    {
        return $this->hasMany(QuoteAccessory::class, 'quote_order_id', 'id');
    }

    /**
     * 关联估价记录
     * @return \think\model\relation\HasMany
     */
    public function quoteRecords()
    {
        return $this->hasMany(QuoteRecord::class, 'quote_order_id', 'id');
    }

    /**
     * 关联最新的估价记录
     * @return \think\model\relation\HasOne
     */
    public function latestQuoteRecord()
    {
        return $this->hasOne(QuoteRecord::class, 'quote_order_id', 'id')->order('create_time', 'desc');
    }

    /**
     * 搜索器：订单编号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchOrderNoAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('order_no', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：用户ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchUserIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('user_id', $value);
        }
    }

    /**
     * 搜索器：分类ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCategoryIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('category_id', $value);
        }
    }

    /**
     * 搜索器：品牌ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchBrandIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('brand_id', $value);
        }
    }

    /**
     * 搜索器：状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：商品名称
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchProductNameAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('product_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            1 => '估价中',
            2 => '待确认',
            3 => '待发货',
            4 => '已完成',
            5 => '已取消'
        ];
        return $statusMap[$data['status']] ?? '未知状态';
    }
}
