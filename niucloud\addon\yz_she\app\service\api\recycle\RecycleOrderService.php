<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\api\recycle;

use addon\yz_she\app\model\recycle\RecycleOrder;
use addon\yz_she\app\service\core\recycle\CoreRecycleOrderService;
use core\base\BaseApiService;

/**
 * 回收订单服务层
 */
class RecycleOrderService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new RecycleOrder();
    }

    /**
     * 创建回收订单
     * @param array $data
     * @return array
     */
    public function create(array $data)
    {
        // 添加会员ID
        $data['member_id'] = $this->member_id;
        
        // 调用核心服务创建订单
        return (new CoreRecycleOrderService())->create($data);
    }

    /**
     * 获取回收订单分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        // 只查询当前会员的订单
        $where['member_id'] = $this->member_id;
        
        $field = 'id,order_no,quote_order_id,category_id,brand_id,product_id,product_name,product_code,product_image,pickup_address_id,voucher_id,expected_price,voucher_amount,final_price,total_amount,express_fee,status,source_type,delivery_type,settlement_status,express_status,express_company,express_number,pickup_contact_name,pickup_contact_phone,pickup_time,quantity,admin_note,create_time,update_time';
        $order = 'create_time desc';

        $search_model = $this->model
            ->withSearch(['order_no', 'status', 'source_type', 'delivery_type'], $where)
            ->with([
                'category' => function($query) {
                    $query->field('id,name,image');
                },
                'brand' => function($query) {
                    $query->field('id,name,logo');
                },
                'product' => function($query) {
                    $query->field('id,name,image,code');
                },
                'pickupAddress' => function($query) {
                    $query->field('id,name,mobile,full_address');
                },
                'voucher' => function($query) {
                    $query->field('id,voucher_no,price,title');
                }
            ])
            ->field($field)
            ->order($order);

        $list = $this->pageQuery($search_model);

        // 处理数据格式
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                $item = $this->formatOrderData($item);
            }
        }

        return $list;
    }

    /**
     * 获取回收订单详情
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id,order_no,quote_order_id,member_id,category_id,brand_id,product_id,product_name,product_code,product_image,pickup_address_id,voucher_id,expected_price,voucher_amount,final_price,total_amount,express_fee,status,source_type,delivery_type,settlement_status,express_status,express_company,express_number,pickup_contact_name,pickup_contact_phone,pickup_time,quantity,admin_note,receive_time,quality_start_time,quality_complete_time,return_time,settlement_time,create_time,update_time';

        $info = $this->model
            ->where([
                ['id', '=', $id],
                ['member_id', '=', $this->member_id]
            ])
            ->with([
                'category' => function($query) {
                    $query->field('id,name,image');
                },
                'brand' => function($query) {
                    $query->field('id,name,logo');
                },
                'product' => function($query) {
                    $query->field('id,name,image,code');
                },
                'pickupAddress' => function($query) {
                    $query->field('id,name,mobile,province_id,city_id,district_id,address,full_address');
                },
                'voucher' => function($query) {
                    $query->field('id,voucher_no,price,title,expire_time');
                },
                'logs' => function($query) {
                    $query->field('id,recycle_order_id,from_status,to_status,operator_type,operator_id,change_reason,remark,create_time')
                          ->order('create_time desc');
                }
            ])
            ->field($field)
            ->findOrEmpty()
            ->toArray();

        if (empty($info)) {
            throw new \Exception('订单不存在');
        }

        return $this->formatOrderData($info);
    }

    /**
     * 更新快递信息
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateExpress(int $id, array $data)
    {
        // 验证订单是否属于当前会员
        $order = $this->model->where([
            ['id', '=', $id],
            ['member_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        // 只有用户自寄的订单才能更新快递信息
        if ($order['delivery_type'] != 2) {
            throw new \Exception('只有自寄订单才能更新快递信息');
        }

        // 只有待收货状态才能更新快递信息
        if ($order['status'] != 2) {
            throw new \Exception('当前状态不允许更新快递信息');
        }

        return (new CoreRecycleOrderService())->updateExpressInfo($id, $data);
    }

    /**
     * 确认收货
     * @param int $id
     * @return bool
     */
    public function confirmReceive(int $id)
    {
        // 验证订单是否属于当前会员
        $order = $this->model->where([
            ['id', '=', $id],
            ['member_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        return (new CoreRecycleOrderService())->receiveOrder($id);
    }

    /**
     * 确认结算
     * @param int $id
     * @return bool
     */
    public function confirmSettlement(int $id)
    {
        // 验证订单是否属于当前会员
        $order = $this->model->where([
            ['id', '=', $id],
            ['member_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        return (new CoreRecycleOrderService())->settlementOrder($id);
    }

    /**
     * 申请退回
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function requestReturn(int $id, string $reason = '')
    {
        // 验证订单是否属于当前会员
        $order = $this->model->where([
            ['id', '=', $id],
            ['member_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        return (new CoreRecycleOrderService())->returnOrder($id, $reason);
    }

    /**
     * 取消订单
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function cancel(int $id, string $reason = '')
    {
        // 验证订单是否属于当前会员
        $order = $this->model->where([
            ['id', '=', $id],
            ['member_id', '=', $this->member_id]
        ])->findOrEmpty();

        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        return (new CoreRecycleOrderService())->cancelOrder($id, $reason);
    }

    /**
     * 格式化订单数据
     * @param array $data
     * @return array
     */
    private function formatOrderData(array $data)
    {
        // 添加状态文本
        $data['status_text'] = RecycleOrder::getStatusText($data['status'] ?? 0);
        $data['source_type_text'] = RecycleOrder::getSourceTypeText($data['source_type'] ?? 0);
        $data['delivery_type_text'] = RecycleOrder::getDeliveryTypeText($data['delivery_type'] ?? 0);
        $data['express_status_text'] = RecycleOrder::getExpressStatusText($data['express_status'] ?? 0);

        // 格式化时间
        $data['create_time_text'] = $this->formatTimestamp($data['create_time'] ?? null);
        $data['pickup_time_text'] = $data['pickup_time'] ?? '';
        $data['receive_time_text'] = $this->formatTimestamp($data['receive_time'] ?? null);
        $data['quality_start_time_text'] = $this->formatTimestamp($data['quality_start_time'] ?? null);
        $data['quality_complete_time_text'] = $this->formatTimestamp($data['quality_complete_time'] ?? null);
        $data['settlement_time_text'] = $this->formatTimestamp($data['settlement_time'] ?? null);

        // 处理关联数据
        $this->formatRelationData($data);

        return $data;
    }

    /**
     * 格式化关联数据
     * @param array &$data
     */
    private function formatRelationData(array &$data)
    {
        // 格式化分类信息
        if (isset($data['category']) && is_array($data['category'])) {
            $data['category_name'] = $data['category']['name'] ?? '';
            $data['category_image'] = $data['category']['image'] ?? '';
        }

        // 格式化品牌信息
        if (isset($data['brand']) && is_array($data['brand'])) {
            $data['brand_name'] = $data['brand']['name'] ?? '';
            $data['brand_logo'] = $data['brand']['logo'] ?? '';
        }

        // 格式化商品信息
        if (isset($data['product']) && is_array($data['product'])) {
            $data['product_name_full'] = $data['product']['name'] ?? '';
            $data['product_image_full'] = $data['product']['image'] ?? '';
            $data['product_code_full'] = $data['product']['code'] ?? '';
        }

        // 格式化取件地址信息
        if (isset($data['pickup_address']) && is_array($data['pickup_address'])) {
            $data['pickup_address_name'] = $data['pickup_address']['name'] ?? '';
            $data['pickup_address_mobile'] = $data['pickup_address']['mobile'] ?? '';
            $data['pickup_address_full'] = $data['pickup_address']['full_address'] ?? '';
        }

        // 格式化加价券信息
        if (isset($data['voucher']) && is_array($data['voucher'])) {
            $data['voucher_title'] = $data['voucher']['title'] ?? '';
            $data['voucher_price'] = $data['voucher']['price'] ?? 0;
            $data['voucher_no'] = $data['voucher']['voucher_no'] ?? '';
        }
    }

    /**
     * 安全的时间戳格式化
     * @param mixed $timestamp
     * @return string
     */
    private function formatTimestamp($timestamp)
    {
        if (empty($timestamp)) {
            return '';
        }

        // 如果是字符串，尝试转换为时间戳
        if (is_string($timestamp)) {
            $timestamp = strtotime($timestamp);
            if ($timestamp === false) {
                return '';
            }
        }

        // 确保是有效的时间戳
        if (!is_numeric($timestamp) || $timestamp <= 0) {
            return '';
        }

        return date('Y-m-d H:i:s', $timestamp);
    }

    /**
     * 获取状态选项
     * @return array
     */
    public function getStatusOptions()
    {
        return RecycleOrder::getStatusOptions();
    }

    /**
     * 获取配送方式选项
     * @return array
     */
    public function getDeliveryTypeOptions()
    {
        return [
            RecycleOrder::DELIVERY_TYPE_EXPRESS => '快递上门',
            RecycleOrder::DELIVERY_TYPE_SELF => '用户自寄'
        ];
    }

    /**
     * 获取订单来源选项
     * @return array
     */
    public function getSourceTypeOptions()
    {
        return [
            RecycleOrder::SOURCE_TYPE_QUOTE_CONFIRM => '估价订单确认',
            RecycleOrder::SOURCE_TYPE_DIRECT => '直接回收',
            RecycleOrder::SOURCE_TYPE_BATCH => '批量下单',
            RecycleOrder::SOURCE_TYPE_BATCH_QUOTE => '批量估价下单'
        ];
    }

    /**
     * 批量创建回收订单
     * @param array $data
     * @return array
     */
    public function batchCreate(array $data)
    {
        if (empty($data['quote_order_ids'])) {
            throw new \Exception('估价订单ID不能为空');
        }

        // 验证估价订单是否存在且属于当前会员
        $quoteOrderModel = new \addon\yz_she\app\model\quote\QuoteOrder();
        $quoteOrders = $quoteOrderModel->where([
            ['id', 'in', $data['quote_order_ids']],
            ['user_id', '=', $this->member_id],
            ['status', '=', 4] // 只能基于已完成的估价订单创建回收订单
        ])->select();

        if ($quoteOrders->isEmpty()) {
            throw new \Exception('没有找到可用的估价订单');
        }

        if (count($quoteOrders) !== count($data['quote_order_ids'])) {
            throw new \Exception('部分估价订单不存在或状态不正确');
        }

        // 验证配送参数
        $this->validateBatchCreateData($data);

        // 记录批量创建开始日志
        \think\facade\Log::info('开始批量创建回收订单', [
            'member_id' => $this->member_id,
            'quote_order_ids' => $data['quote_order_ids'],
            'delivery_type' => $data['delivery_type'],
            'pickup_address_id' => $data['pickup_address_id'] ?? 0,
            'express_company' => $data['express_company'] ?? '',
            'express_number' => $data['express_number'] ?? ''
        ]);

        // 生成批量订单组标识
        $batchGroupId = $this->generateBatchGroupId();

        // 根据配送方式设置初始状态
        if ($data['delivery_type'] == 1) {
            // 快递上门：待取件
            $initialStatus = RecycleOrder::STATUS_PICKUP_PENDING;
        } else if ($data['delivery_type'] == 2 && !empty($data['express_number'])) {
            // 自行寄出且有快递单号：直接进入待质检状态
            $initialStatus = RecycleOrder::STATUS_QUALITY_PENDING;
        } else {
            // 其他情况：待收货
            $initialStatus = RecycleOrder::STATUS_RECEIVE_PENDING;
        }

        // 开启事务
        $this->model->startTrans();
        try {
            $successCount = 0;
            $failedOrders = [];
            $createdOrders = [];

            foreach ($quoteOrders as $quoteOrder) {
                try {
                    // 构建回收订单数据
                    $recycleData = [
                        // 不设置order_no，让核心服务自动生成唯一订单号
                        'quote_order_id' => $quoteOrder->id,
                        'member_id' => $this->member_id,
                        'category_id' => $quoteOrder->category_id ?? 0,
                        'brand_id' => $quoteOrder->brand_id ?? 0,
                        'product_id' => $quoteOrder->product_id ?? 0,
                        'product_name' => $quoteOrder->product_name,
                        'product_code' => $quoteOrder->product_code ?? '',
                        'product_image' => $quoteOrder->product_image ?? '',
                        'status' => $initialStatus, // 根据配送方式设置初始状态
                        'pickup_address_id' => $data['pickup_address_id'] ?? 0,
                        'expected_price' => floatval($quoteOrder->quote_price),
                        'final_price' => null, // 创建时不设置最终价格
                        'total_amount' => null, // 创建时不设置结算金额
                        'source_type' => RecycleOrder::SOURCE_TYPE_BATCH_QUOTE, // 批量估价下单
                        'delivery_type' => $data['delivery_type'],
                        'pickup_contact_name' => '', // 从地址信息中获取
                        'pickup_contact_phone' => '', // 从地址信息中获取
                        'pickup_address_detail' => '', // 从地址信息中获取
                        'pickup_time' => $data['pickup_time'] ?? '',
                        'express_company' => $data['express_company'] ?? '',
                        'express_number' => $data['express_number'] ?? '',
                        'admin_note' => ($data['admin_note'] ?? '') . ($data['admin_note'] ? ' | ' : '') . "批量组:{$batchGroupId}",
                        'quantity' => 1,
                        'voucher_id' => 0,
                        'voucher_amount' => 0,
                        'express_fee' => 0
                    ];

                    // 如果是快递上门，需要获取地址信息
                    if ($data['delivery_type'] == 1 && !empty($data['pickup_address_id'])) {
                        $addressModel = new \addon\yz_she\app\model\member\MemberAddress();
                        $address = $addressModel->where([
                            ['id', '=', $data['pickup_address_id']],
                            ['member_id', '=', $this->member_id]
                        ])->findOrEmpty();

                        if (!$address->isEmpty()) {
                            $recycleData['pickup_contact_name'] = $address->name;
                            $recycleData['pickup_contact_phone'] = $address->mobile;
                            $recycleData['pickup_address_detail'] = $address->province . $address->city . $address->district . $address->address;
                        } else {
                            throw new \Exception('取件地址不存在');
                        }
                    } else if ($data['delivery_type'] == 1) {
                        throw new \Exception('快递上门需要选择取件地址');
                    }

                    // 调用核心服务创建回收订单
                    $result = (new \addon\yz_she\app\service\core\recycle\CoreRecycleOrderService())->create($recycleData);

                    if ($result) {
                        $successCount++;
                        $createdOrders[] = [
                            'quote_order_id' => $quoteOrder->id,
                            'recycle_order_id' => $result['id'] ?? null,
                            'order_no' => $result['order_no'] ?? ''
                        ];
                    }

                } catch (\Exception $e) {
                    // 记录详细的错误信息
                    \think\facade\Log::error('批量创建回收订单失败', [
                        'quote_order_id' => $quoteOrder->id,
                        'quote_order_no' => $quoteOrder->order_no,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'recycle_data' => $recycleData ?? []
                    ]);

                    $failedOrders[] = [
                        'quote_order_id' => $quoteOrder->id,
                        'quote_order_no' => $quoteOrder->order_no,
                        'error' => $e->getMessage()
                    ];
                }
            }

            if ($successCount === 0) {
                throw new \Exception('批量创建回收订单失败');
            }

            $this->model->commit();

            return [
                'success_count' => $successCount,
                'total_count' => count($quoteOrders),
                'failed_orders' => $failedOrders,
                'created_orders' => $createdOrders
            ];

        } catch (\Exception $e) {
            $this->model->rollback();
            throw $e;
        }
    }

    /**
     * 验证批量创建数据
     * @param array $data
     * @throws \Exception
     */
    private function validateBatchCreateData(array $data)
    {
        if (!in_array($data['delivery_type'], [1, 2])) {
            throw new \Exception('配送方式无效');
        }

        if ($data['delivery_type'] == 1) {
            // 快递上门验证
            if (empty($data['pickup_address_id'])) {
                throw new \Exception('快递上门需要选择取件地址');
            }
        } else {
            // 用户自寄验证
            if (empty($data['express_company'])) {
                throw new \Exception('用户自寄需要选择快递公司');
            }
            if (empty($data['express_number'])) {
                throw new \Exception('用户自寄需要填写快递单号');
            }
        }
    }

    /**
     * 生成批量订单编号
     * @return string
     */
    private function generateBatchOrderNo()
    {
        // 生成格式：RC + 年月日 + 时分秒 + 随机数
        $prefix = 'RC';
        $date = date('Ymd');
        $time = date('His');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . $date . $time . $random;
    }
}
