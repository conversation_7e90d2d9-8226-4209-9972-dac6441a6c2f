<template>
  <view class="evaluate-orders-page">
    <!-- 顶部状态筛选 -->
    <scroll-view class="status-tabs-container" scroll-x="true" show-scrollbar="false">
      <view class="status-tabs">
        <view
          class="tab-item"
          :class="{ active: currentStatus === item.value }"
          v-for="item in statusTabs"
          :key="item.value"
          @click="switchStatus(item.value)"
        >
          <text class="tab-text">{{ item.label }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view
        class="order-item"
        v-for="order in filteredOrders"
        :key="order.id"
        @click="viewOrderDetail(order)"
      >
        <view class="order-content" :class="{ 'with-checkbox': currentStatus === 3 }">
          <view class="order-header">
            <!-- 待发货状态的勾选框与订单号对齐 -->
            <view class="order-header-left">
              <view
                class="checkbox"
                v-if="currentStatus === 3"
                :class="{ checked: selectedOrders.includes(order.id) }"
                @click.stop="toggleOrderSelection(order.id)"
              >
                <text class="checkbox-icon" v-if="selectedOrders.includes(order.id)">✓</text>
              </view>
              <text class="order-no">订单号: {{ order.order_no }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(order.status)">
              <text class="status-text">{{ getStatusText(order.status) }}</text>
            </view>
          </view>

          <view class="order-time">
            <text class="time-text">创建时间: {{ formatTime(order.create_time) }}</text>
          </view>

          <view class="product-info">
            <!-- 根据商品信息显示逻辑：优先显示商品信息，没有则显示品牌信息 -->
            <image
              :src="getDisplayImage(order)"
              class="product-image"
              mode="aspectFit"
            ></image>
            <view class="product-details">
              <text class="product-name">{{ getDisplayName(order) }}</text>
              <text class="product-code" v-if="getDisplayCode(order)">{{ getDisplayCode(order) }}</text>
            </view>
            <view class="price-info">
              <text class="price-label">{{ getPriceLabel(order.status) }}</text>
              <view class="price-amount">
                <text class="currency">¥</text>
                <text class="price-value">{{ order.quote_price || '待估价' }}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 (待发货状态不显示查看物流按钮) -->
          <view class="order-actions" v-if="hasActions(order.status) && currentStatus !== 3">
            <view
              class="action-btn"
              v-for="action in getOrderActions(order.status)"
              :key="action.type"
              :class="action.type"
              @click.stop="handleAction(order, action.type)"
            >
              <text class="action-text">{{ action.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredOrders.length === 0">
        <view class="empty-icon">📋</view>
        <text class="empty-text">暂无{{ getStatusText(currentStatus) }}订单</text>
      </view>
    </view>

    <!-- 底部加载更多 -->
    <view class="load-more" v-if="hasMore && filteredOrders.length > 0 && currentStatus !== 3" @click="loadOrderList()">
      <text class="load-text">{{ loading ? '加载中...' : '加载更多...' }}</text>
    </view>

    <!-- 待发货批量操作底部栏 -->
    <view class="batch-actions-bar" v-if="currentStatus === 3 && filteredOrders.length > 0">
      <view class="select-all-container">
        <view
          class="checkbox"
          :class="{ checked: isAllSelected }"
          @click="toggleSelectAll"
        >
          <text class="checkbox-icon" v-if="isAllSelected">✓</text>
        </view>
        <view class="select-text-container">
          <text class="select-all-text">全选</text>
          <text class="selected-count" v-if="selectedOrders.length > 0">
            已选{{ selectedOrders.length }}件，预计可获得 ¥{{ selectedTotalPrice }}
          </text>
        </view>
      </view>

      <view
        class="batch-ship-btn"
        :class="{ disabled: selectedOrders.length === 0 }"
        @click="batchShip"
      >
        <text class="batch-ship-text">批量发货</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderList, confirmQuoteOrder, cancelQuoteOrder } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 状态标签页
const statusTabs = ref([
  { label: '全部', value: '' },
  { label: '估价中', value: 1 },
  { label: '待确认', value: 2 },
  { label: '待发货', value: 3 },
  { label: '已完成', value: 4 },
  { label: '已取消', value: 5 }
])

// 当前选中状态
const currentStatus = ref('')

// 加载状态
const loading = ref(false)

// 是否有更多数据
const hasMore = ref(false)

// 分页参数
const pageParams = ref({
  page: 1,
  limit: 10
})

// 选中的订单
const selectedOrders = ref([])

// 订单数据
const orders = ref([])

// 全选状态
const isAllSelected = computed(() => {
  const shippingOrders = orders.value.filter(order => order.status === 3)
  return shippingOrders.length > 0 && selectedOrders.value.length === shippingOrders.length
})

// 选中订单总价
const selectedTotalPrice = computed(() => {
  return orders.value
    .filter(order => selectedOrders.value.includes(order.id))
    .reduce((total, order) => total + parseFloat(order.quote_price || 0), 0)
    .toFixed(2)
})

// 加载订单列表
const loadOrderList = async (refresh = false) => {
  if (loading.value) return

  if (refresh) {
    pageParams.value.page = 1
    orders.value = []
  }

  loading.value = true

  try {
    const params = {
      status: currentStatus.value,
      page: pageParams.value.page,
      limit: pageParams.value.limit
    }

    const response = await getQuoteOrderList(params)

    if (response.code === 1) {
      const newOrders = response.data.data || []

      if (refresh) {
        orders.value = newOrders
      } else {
        orders.value.push(...newOrders)
      }

      // 检查是否还有更多数据
      hasMore.value = newOrders.length === pageParams.value.limit

      if (hasMore.value) {
        pageParams.value.page++
      }
    } else {
      uni.showToast({
        title: response.msg || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 过滤后的订单列表（由于后端已经按状态过滤，这里直接返回）
const filteredOrders = computed(() => {
  return orders.value
})

// 切换状态
const switchStatus = (status: any) => {
  currentStatus.value = status
  // 切换状态时清空选中项
  selectedOrders.value = []
  // 重新加载数据
  loadOrderList(true)
}

// 切换订单选中状态
const toggleOrderSelection = (orderId: number) => {
  const index = selectedOrders.value.indexOf(orderId)
  if (index > -1) {
    selectedOrders.value.splice(index, 1)
  } else {
    selectedOrders.value.push(orderId)
  }
}

// 切换全选状态
const toggleSelectAll = () => {
  const shippingOrders = orders.value.filter(order => order.status === 3)
  if (isAllSelected.value) {
    selectedOrders.value = []
  } else {
    selectedOrders.value = shippingOrders.map(order => order.id)
  }
}

// 批量发货
const batchShip = () => {
  if (selectedOrders.value.length === 0) {
    uni.showToast({
      title: '请选择要发货的订单',
      icon: 'none'
    })
    return
  }

  // 跳转到批量发货页面
  const orderIds = encodeURIComponent(JSON.stringify(selectedOrders.value))
  uni.navigateTo({
    url: `/addon/yz_she/pages/order/detail/quote-order?orderIds=${orderIds}`
  })
}

// 商品信息显示逻辑：优先显示商品信息，没有则显示品牌信息
const getDisplayImage = (order: any) => {
  // 如果有商品图片，优先显示商品图片
  if (order.product_image) {
    return img(order.product_image)
  }
  // 如果有品牌信息，显示品牌logo
  if (order.brand && order.brand.logo) {
    return img(order.brand.logo)
  }
  // 默认占位图
  return '/static/images/default-product.png'
}

const getDisplayName = (order: any) => {
  // 如果有商品名称，优先显示商品名称
  if (order.product_name) {
    return order.product_name
  }
  // 如果有品牌信息，显示品牌名称
  if (order.brand && order.brand.brand_name) {
    return order.brand.brand_name
  }
  // 如果有分类信息，显示分类名称
  if (order.category && order.category.name) {
    return order.category.name
  }
  return '未知商品'
}

const getDisplayCode = (order: any) => {
  // 如果有商品编码，优先显示商品编码
  if (order.product_code) {
    return order.product_code
  }
  // 如果有品牌编码，显示品牌编码
  if (order.brand && order.brand.brand_code) {
    return order.brand.brand_code
  }
  return ''
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  const statusMap = {
    1: 'status-evaluating',
    2: 'status-pending',
    3: 'status-shipping',
    4: 'status-completed',
    5: 'status-cancelled'
  }
  return statusMap[status] || ''
}

// 获取状态文字
const getStatusText = (status: number | string) => {
  const statusMap = {
    '': '全部',
    1: '估价中',
    2: '待确认',
    3: '待发货',
    4: '已完成',
    5: '已取消'
  }
  return statusMap[status] || status
}

// 获取价格标签
const getPriceLabel = (status: number) => {
  const labelMap = {
    1: '预估价格',
    2: '估价金额',
    3: '预估价格',
    4: '预估价格',
    5: '估价金额'
  }
  return labelMap[status] || '估价金额'
}

// 判断是否有操作按钮
const hasActions = (status: number) => {
  return [1, 2, 3].includes(status)
}

// 获取订单操作按钮 (待发货状态不显示按钮)
const getOrderActions = (status: number) => {
  const actionsMap = {
    1: [
      { type: 'cancel', text: '取消订单' }
    ],
    2: [
      { type: 'cancel', text: '取消订单' },
      { type: 'confirm', text: '确认回收' }
    ]
    // 待发货状态不显示任何按钮，改为批量操作
  }
  return actionsMap[status] || []
}

// 处理操作按钮点击
const handleAction = (order: any, actionType: string) => {
  console.log('处理操作:', order.orderNo, actionType)
  
  switch (actionType) {
    case 'cancel':
      cancelOrder(order)
      break
    case 'confirm':
      confirmOrder(order)
      break
    case 'logistics':
      viewLogistics(order)
      break
  }
}

// 取消订单
const cancelOrder = async (order: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await cancelQuoteOrder(order.id, '用户主动取消')
          if (response.code === 1) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            // 重新加载数据
            loadOrderList(true)
          } else {
            uni.showToast({
              title: response.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({
            title: '取消失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 确认订单
const confirmOrder = async (order: any) => {
  uni.showModal({
    title: '确认回收',
    content: `确认以¥${order.quote_price || '待估价'}的价格回收此商品？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await confirmQuoteOrder(order.id)
          if (response.code === 1) {
            uni.showToast({
              title: '确认成功，请寄出商品',
              icon: 'success'
            })
            // 重新加载数据
            loadOrderList(true)
          } else {
            uni.showToast({
              title: response.msg || '确认失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('确认订单失败:', error)
          uni.showToast({
            title: '确认失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 查看物流
const viewLogistics = (order: any) => {
  uni.navigateTo({
    url: `/addon/yz_she/pages/logistics/detail?orderNo=${order.orderNo}`
  })
}

// 查看订单详情
const viewOrderDetail = (order: any) => {
  uni.navigateTo({
    url: `/addon/yz_she/pages/order/detail/quote-detail?id=${order.id}&status=${order.status}`
  })
}

// 页面参数处理
onLoad((options: any) => {
  console.log('页面参数:', options)

  // 如果传入了状态参数，设置当前状态
  if (options.status) {
    const status = parseInt(options.status)
    if ([1, 2, 3, 4, 5].includes(status)) {
      currentStatus.value = status
      console.log('设置当前状态为:', status)
    }
  }
})

// 页面加载
onMounted(() => {
  // 页面初始化
  console.log('报价单列表页面加载完成，当前状态:', currentStatus.value)
  loadOrderList(true)
})
</script>

<style lang="scss" scoped>
.evaluate-orders-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
}

// 状态标签页容器
.status-tabs-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
}

.status-tabs {
  display: flex;
  padding: 0 16rpx; // 减少左边距，让全部标签更靠左

  .tab-item {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 20rpx;
    position: relative;
    min-width: 100rpx;

    .tab-text {
      font-size: 28rpx;
      color: #666;
      transition: color 0.3s ease;
      white-space: nowrap;
    }

    &.active {
      .tab-text {
        color: #16a085;
        font-weight: 600;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: linear-gradient(90deg, #16a085, #1abc9c);
        border-radius: 2rpx;
        box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.4);
      }
    }
  }
}

// 订单列表
.order-list {
  padding: 20rpx 24rpx;

  .order-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(22, 160, 133, 0.08);
    border: 1rpx solid rgba(22, 160, 133, 0.1);

    .order-content {
      padding: 32rpx 24rpx;
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .order-header-left {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .checkbox {
          width: 32rpx;
          height: 32rpx;
          border: 2rpx solid #d9d9d9;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &.checked {
            background: linear-gradient(135deg, #16a085, #1abc9c);
            border-color: #16a085;

            .checkbox-icon {
              color: #fff;
              font-size: 20rpx;
              font-weight: bold;
            }
          }
        }
      }

      .order-no {
        font-size: 26rpx;
        color: #333;
        font-weight: 600;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;

        .status-text {
          font-weight: 500;
        }

        &.status-evaluating {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-pending {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.status-shipping {
          background-color: #fff1f0;
          color: #ff4d4f;
        }

        &.status-completed {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.status-cancelled {
          background-color: #f5f5f5;
          color: #999;
        }
      }
    }

    .order-time {
      margin-bottom: 20rpx;

      .time-text {
        font-size: 24rpx;
        color: #999;
      }
    }

    .product-info {
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-bottom: 24rpx;

      .product-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        background-color: #f0f0f0;
      }

      .product-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .product-name {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-code {
          font-size: 22rpx;
          color: #999;
        }
      }

      .price-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4rpx;

        .price-label {
          font-size: 22rpx;
          color: #666;
        }

        .price-amount {
          display: flex;
          align-items: baseline;
          gap: 2rpx;

          .currency {
            font-size: 22rpx;
            color: #16a085;
            font-weight: 600;
          }

          .price-value {
            font-size: 32rpx;
            color: #16a085;
            font-weight: 700;
          }
        }
      }
    }

    // 操作按钮
    .order-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16rpx;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 20rpx;

      .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &.cancel {
          background-color: #fff;
          border: 1rpx solid #d9d9d9;
          color: #666;

          &:active {
            background-color: #f5f5f5;
          }
        }

        &.confirm {
          background: linear-gradient(135deg, #0d7377, #14a085);
          color: #fff;

          &:active {
            background: linear-gradient(135deg, #0a5d61, #117a65);
          }
        }

        &.logistics {
          background-color: #fff;
          border: 1rpx solid #16a085;
          color: #16a085;

          &:active {
            background: linear-gradient(135deg, #f0fffe, #e8f8f5);
          }
        }

        .action-text {
          letter-spacing: 1rpx;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 加载更多
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;

  .load-text {
    font-size: 26rpx;
    color: #999;
  }
}

// 批量操作底部栏
.batch-actions-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 100;

  .select-all-container {
    display: flex;
    align-items: center;
    gap: 16rpx;
    flex: 1;

    .checkbox {
      width: 32rpx;
      height: 32rpx;
      border: 2rpx solid #d9d9d9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &.checked {
        background: linear-gradient(135deg, #16a085, #1abc9c);
        border-color: #16a085;

        .checkbox-icon {
          color: #fff;
          font-size: 20rpx;
          font-weight: bold;
        }
      }
    }

    .select-text-container {
      display: flex;
      flex-direction: column;
      gap: 4rpx;
    }

    .select-all-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      line-height: 1.2;
    }

    .selected-count {
      font-size: 22rpx;
      color: #16a085;
      line-height: 1.2;
    }
  }

  .batch-ship-btn {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    border-radius: 32rpx;
    min-width: 160rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(13, 115, 119, 0.4);

    &.disabled {
      background: #f5f5f5;
      box-shadow: none;

      .batch-ship-text {
        color: #ccc;
      }
    }

    &:not(.disabled):active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
    }

    .batch-ship-text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 600;
      letter-spacing: 1rpx;
    }
  }
}

// 为批量操作栏留出底部空间
.evaluate-orders-page {
  padding-bottom: 120rpx;
}

// 响应式适配
@media screen and (max-width: 375px) {
  .status-tabs {
    padding: 0 12rpx; // 小屏幕进一步减少左边距

    .tab-item {
      padding: 20rpx 16rpx;
      min-width: 80rpx;

      .tab-text {
        font-size: 26rpx;
      }
    }
  }

  .order-list {
    padding: 16rpx 24rpx;

    .order-item {
      .order-content {
        padding: 24rpx 20rpx;

        .order-header {
          .order-header-left {
            gap: 10rpx;

            .checkbox {
              width: 28rpx;
              height: 28rpx;

              &.checked {
                .checkbox-icon {
                  font-size: 18rpx;
                }
              }
            }
          }
        }
      }

      .product-info {
        .product-image {
          width: 100rpx;
          height: 100rpx;
        }

        .product-details {
          .product-name {
            font-size: 24rpx;
          }

          .product-code {
            font-size: 20rpx;
          }
        }

        .price-info {
          .price-amount {
            .price-value {
              font-size: 28rpx;
            }
          }
        }
      }

      .order-actions {
        .action-btn {
          padding: 10rpx 20rpx;
          font-size: 22rpx;
        }
      }
    }
  }

  .batch-actions-bar {
    padding: 16rpx 24rpx;

    .select-all-container {
      gap: 12rpx;

      .select-text-container {
        gap: 2rpx;
      }

      .select-all-text {
        font-size: 26rpx;
      }

      .selected-count {
        font-size: 20rpx;
      }
    }

    .batch-ship-btn {
      padding: 14rpx 28rpx;
      min-width: 140rpx;

      .batch-ship-text {
        font-size: 26rpx;
      }
    }
  }
}
</style>
