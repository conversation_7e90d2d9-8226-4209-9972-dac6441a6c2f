<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

use app\api\middleware\ApiCheckToken;
use app\api\middleware\ApiLog;
use app\api\middleware\ApiChannel;
use think\facade\Route;


/**
 * 柚子奢侈品回收
 */
Route::group('yz_she', function() {
    /***************************************************** hello world ****************************************************/
    Route::get('hello_world', 'addon\yz_she\app\api\controller\hello_world\Index@index');

    /***************************************************** 商品分类相关接口 ****************************************************/
    Route::get('goods/category/list', 'addon\yz_she\app\api\controller\goods\Category@lists');
    Route::get('goods/category/:id', 'addon\yz_she\app\api\controller\goods\Category@info');

    /***************************************************** 商品品牌相关接口 ****************************************************/
    Route::get('goods/brand/list', 'addon\yz_she\app\api\controller\goods\Brand@lists');
    Route::get('goods/brand/:id', 'addon\yz_she\app\api\controller\goods\Brand@info');

    /***************************************************** 商品相关接口 ****************************************************/
    Route::get('goods/list', 'addon\yz_she\app\api\controller\goods\Goods@lists');
    Route::get('goods/:id', 'addon\yz_she\app\api\controller\goods\Goods@info');
    Route::get('goods/brand/hot', 'addon\yz_she\app\api\controller\goods\Goods@brandHot');
    Route::get('goods/brand/all', 'addon\yz_she\app\api\controller\goods\Goods@brandAll');
    Route::get('goods/brand/search', 'addon\yz_she\app\api\controller\goods\Goods@brandSearch');
    Route::get('goods/debug', 'addon\yz_she\app\api\controller\goods\Goods@debug');

    /***************************************************** 回收标准相关接口 ****************************************************/
    Route::get('recycle_standard/lists', 'addon\yz_she\app\api\controller\recycle\RecycleStandard@lists');
    Route::get('recycle_standard/info/:id', 'addon\yz_she\app\api\controller\recycle\RecycleStandard@info');
    Route::get('recycle_standard/:category_id', 'addon\yz_she\app\api\controller\recycle\RecycleStandard@getByCategory');
    Route::post('recycle_standard/calculate', 'addon\yz_she\app\api\controller\recycle\RecycleStandard@calculate');
    Route::get('recycle_standard/recommend', 'addon\yz_she\app\api\controller\recycle\RecycleStandard@recommend');

    /***************************************************** 分类配置相关接口 ****************************************************/
    Route::get('category/lists', 'addon\yz_she\app\api\controller\config\Category@lists');
    Route::get('category/tree', 'addon\yz_she\app\api\controller\config\Category@tree');
    Route::get('category/info/:id', 'addon\yz_she\app\api\controller\config\Category@info');
    Route::get('category/photos/:category_id', 'addon\yz_she\app\api\controller\config\Category@getPhotoConfig');
    Route::get('category/accessories/:category_id', 'addon\yz_she\app\api\controller\config\Category@getAccessoryConfig');
    Route::get('category/config', 'addon\yz_she\app\api\controller\config\Category@config');

    /***************************************************** 估价订单相关接口 ****************************************************/
    Route::post('quote/order', 'addon\yz_she\app\api\controller\quote\QuoteOrder@create');
    Route::get('quote/order/list', 'addon\yz_she\app\api\controller\quote\QuoteOrder@lists');
    Route::get('quote/order/status', 'addon\yz_she\app\api\controller\quote\QuoteOrder@getStatus');
    Route::get('quote/order/:id', 'addon\yz_she\app\api\controller\quote\QuoteOrder@detail');
    Route::post('quote/order/:id/confirm', 'addon\yz_she\app\api\controller\quote\QuoteOrder@confirm');
    Route::post('quote/order/:id/cancel', 'addon\yz_she\app\api\controller\quote\QuoteOrder@cancel');
    Route::post('quote/order/:id/status', 'addon\yz_she\app\api\controller\quote\QuoteOrder@updateStatus');
    Route::post('quote/order/batch/detail', 'addon\yz_she\app\api\controller\quote\QuoteOrder@batchDetail');
    Route::post('quote/order/batch/ship', 'addon\yz_she\app\api\controller\quote\QuoteOrder@batchShip');

})->middleware(ApiChannel::class)
    ->middleware(ApiCheckToken::class, false) //false表示不验证登录
    ->middleware(ApiLog::class);



Route::group('yz_she', function() {
    /***************************************************** 用户加价券相关接口 ****************************************************/
    Route::get('member/voucher', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@lists');
    Route::get('member/voucher/count', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@count');
    Route::get('member/voucher/:id', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@detail');
    Route::post('member/voucher/calculate', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@calculate');
    Route::post('member/voucher/check', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@check');
    Route::post('member/voucher/use', 'addon\yz_she\app\api\controller\voucher\MemberVoucher@use');

    /***************************************************** 回收订单相关接口 ****************************************************/
    Route::post('recycle_order', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@create');
    Route::get('recycle_order/list', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@lists');
    Route::get('recycle_order/status', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@getStatus');
    Route::get('recycle_order/delivery_types', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@getDeliveryTypes');
    Route::get('recycle_order/source_types', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@getSourceTypes');
    Route::get('recycle_order/:id', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@detail');
    Route::post('recycle_order/:id/express', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@updateExpress');
    Route::post('recycle_order/:id/receive', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@confirmReceive');
    Route::post('recycle_order/:id/settlement', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@confirmSettlement');
    Route::post('recycle_order/:id/return', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@requestReturn');
    Route::post('recycle_order/:id/cancel', 'addon\yz_she\app\api\controller\recycle\RecycleOrder@cancel');

})->middleware(ApiChannel::class)
    ->middleware(ApiCheckToken::class, true) //表示验证登录
    ->middleware(ApiLog::class);

