<template>
  <view class="batch-order-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <text class="nav-title">批量发货</text>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 批量订单概览 -->
      <view class="batch-overview">
        <view class="overview-header">
          <text class="overview-title">批量发货订单</text>
          <text class="overview-count">共{{ orderList.length }}个订单</text>
        </view>

        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-label">总金额</text>
            <text class="stat-value">¥{{ totalAmount }}</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-label">预计收益</text>
            <text class="stat-value">¥{{ totalAmount }}</text>
          </view>
        </view>
      </view>

      <!-- 订单列表 -->
      <view class="order-list-section">
        <view class="section-header">
          <text class="section-title">发货订单列表</text>
        </view>

        <view class="order-list">
          <view
            v-for="(order, index) in orderList"
            :key="order.id"
            class="order-item"
          >
            <view class="order-content">
              <image
                class="product-image"
                :src="img(order.product_image)"
                mode="aspectFill"
              />
              <view class="product-info">
                <text class="product-name">{{ order.product_name }}</text>
                <text class="order-no">订单号：{{ order.order_no }}</text>
                <view class="price-info">
                  <text class="price-label">成交价格</text>
                  <text class="price-value">¥{{ order.quote_price }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 配送方式选择 -->
      <view class="shipping-section">
        <!-- 配送方式 -->
        <view class="delivery-section">
          <view class="delivery-options">
            <view
              class="delivery-option"
              :class="{ active: selectedDelivery === 'pickup' }"
              @click="selectDelivery('pickup')"
            >
              <view class="option-content">
                <text class="option-name">快递上门</text>
                <view class="option-tag">
                  <text class="tag-text">免费</text>
                </view>
              </view>
            </view>
            <view
              class="delivery-option"
              :class="{ active: selectedDelivery === 'self' }"
              @click="selectDelivery('self')"
            >
              <text class="option-name">自行寄出</text>
            </view>
          </view>

          <!-- 快递上门内容 -->
          <view v-if="selectedDelivery === 'pickup'" class="pickup-content">
            <!-- 地址选择 -->
            <view class="address-item" @click="selectAddress">
              <view class="address-icon">
                <u-icon name="map" color="#333" size="18"></u-icon>
              </view>
              <view class="address-content">
                <text class="address-text" v-if="!selectedAddress">请选择取件地址</text>
                <view v-else class="selected-address">
                  <text class="address-name">{{ selectedAddress.name }} {{ selectedAddress.mobile }}</text>
                  <text class="address-detail">{{ selectedAddress.full_address }}</text>
                </view>
              </view>
              <view class="divider-line"></view>
              <text class="address-action">地址簿</text>
            </view>

            <!-- 预约时间 -->
            <view class="time-item" :class="{ disabled: !selectedAddress }" @click="showTimeModal">
              <view class="time-icon">
                <svg viewBox="0 0 1024 1024" width="32" height="32">
                  <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z" :fill="selectedAddress ? '#333' : '#ccc'"/>
                </svg>
              </view>
              <text class="time-text">期望上门时间</text>
              <text class="time-action" :class="{ disabled: !selectedAddress }">
                {{ !selectedAddress ? '请先选择地址' : (selectedTime || '尽快上门') }} >
              </text>
            </view>
          </view>

          <!-- 自行寄出内容 -->
          <view v-if="selectedDelivery === 'self'" class="self-content">
            <!-- 收货地址 -->
            <view class="address-item">
              <view class="address-icon orange-bg">
                <text class="address-text-icon">收</text>
              </view>
              <view class="address-info">
                <text class="address-name">放心星仓库 13060000687</text>
                <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
              </view>
              <text class="copy-btn" @click="copyAddress">复制</text>
            </view>

            <!-- 快递公司 -->
            <view class="express-item" @click="showExpressModal">
              <view class="express-icon">
                <u-icon name="car" color="#333" size="32"></u-icon>
              </view>
              <text class="express-text">快递公司</text>
              <text class="express-action">{{ selectedExpress || '请选择快递公司' }} ></text>
            </view>

            <!-- 快递单号 -->
            <view class="tracking-item">
              <view class="tracking-icon">
                <u-icon name="order" color="#333" size="32"></u-icon>
              </view>
              <text class="tracking-text">快递单号</text>
              <input
                class="tracking-input"
                v-model="trackingNumber"
                placeholder="请输入快递单号"
                maxlength="30"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="batch-submit-button" :class="{ submitting: submitting }" @click="confirmBatchShipping">
        <view class="button-content">
          <view v-if="!submitting" class="normal-content">
            <view class="button-icon">
              <text class="icon-text">✓</text>
            </view>
            <text class="button-text">确认批量发货</text>
          </view>
          <view v-else class="loading-content">
            <view class="loading-spinner"></view>
            <text class="loading-text">提交中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <view class="express-modal" v-if="showExpressSelect" @click="hideExpressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择快递公司</text>
          <view class="close-btn" @click="hideExpressModal">×</view>
        </view>

        <view class="express-list">
          <view
            class="express-option"
            v-for="express in expressList"
            :key="express"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view class="express-check" v-if="selectedExpress === express">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择弹窗 -->
    <view class="time-modal" v-if="showTime" @click="hideTimeModal">
      <view class="time-modal-content" @click.stop>
        <view class="time-header">
          <text class="time-title">选择上门时间</text>
          <view class="close-btn" @click="hideTimeModal">×</view>
        </view>

        <!-- 时间选择内容 -->
        <view class="time-picker-container">
          <!-- 左侧日期列表 -->
          <view class="date-list">
            <view
              class="date-item"
              v-for="(date, index) in dateOptions"
              :key="index"
              :class="{ active: selectedDateIndex === index }"
              @click="selectDate(index)"
            >
              <text class="date-text">{{ date.label }}</text>
            </view>
          </view>

          <!-- 右侧时间段列表 -->
          <view class="time-list">
            <view
              class="time-item"
              v-for="(timeSlot, index) in currentTimeSlots"
              :key="index"
              :class="{
                active: selectedTimeIndex === index,
                disabled: timeSlot.disabled,
                urgent: timeSlot.isUrgent
              }"
              @click="selectTimeSlot(index)"
            >
              <text class="time-text">{{ timeSlot.label }}</text>
              <text class="time-desc" v-if="timeSlot.desc">{{ timeSlot.desc }}</text>
            </view>
          </view>
        </view>

        <!-- 确认按钮 -->
        <view class="time-confirm">
          <view class="confirm-btn" :class="{ disabled: !canConfirm }" @click="confirmTime">
            <text class="confirm-text">确认</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { img } from '@/utils/common'
import { getBatchQuoteOrderDetails, batchShipQuoteOrders } from '@/addon/yz_she/api/quote'

// 页面参数
const pageParams = ref<any>({})

// 订单列表数据
const orderList = ref<any[]>([])

// 配送方式相关
const selectedDelivery = ref<string>('pickup') // 'pickup' | 'self'
const selectedAddress = ref<any>(null)
const selectedTime = ref<string>('尽快上门')
const selectedExpress = ref<string>('') // 选中的快递公司
const trackingNumber = ref<string>('') // 快递单号
const showExpressSelect = ref<boolean>(false) // 控制快递公司选择弹窗
const showTime = ref<boolean>(false) // 控制时间选择弹窗
const selectedDateIndex = ref<number>(0) // 选中的日期索引
const selectedTimeIndex = ref<number>(-1) // 选中的时间段索引
const submitting = ref<boolean>(false) // 提交状态

// 日期选项
const dateOptions = ref<any[]>([])
// 当前选中日期的时间段
const currentTimeSlots = ref<any[]>([])
// 是否可以确认
const canConfirm = computed(() => selectedTimeIndex.value >= 0 && !currentTimeSlots.value[selectedTimeIndex.value]?.disabled)

// 快递公司列表
const expressList = [
  '顺丰速运',
  '圆通速递',
  '中通快递',
  '申通快递',
  '韵达速递',
  '百世快递',
  '德邦快递',
  '京东快递',
  '邮政EMS',
  '极兔速递'
]

// 计算总金额
const totalAmount = computed(() => {
  return orderList.value.reduce((sum, order) => {
    return sum + parseFloat(order.quote_price || 0)
  }, 0).toFixed(2)
})

// 页面加载
onLoad((options) => {
  console.log('批量发货页面参数:', options)
  pageParams.value = options || {}

  // 获取传递的订单ID列表
  if (options.orderIds) {
    const orderIds = JSON.parse(decodeURIComponent(options.orderIds))
    loadOrderList(orderIds)
  }

  // 初始化时间选项
  generateDateOptions()
})

// 加载订单列表
const loadOrderList = async (orderIds: number[]) => {
  try {
    console.log('开始加载订单列表，订单IDs:', orderIds)

    // 调用API获取批量订单详情
    const response = await getBatchQuoteOrderDetails(orderIds)

    console.log('API响应:', response)

    if (response.code === 1) {
      orderList.value = response.data
      console.log('订单列表加载成功:', orderList.value)
    } else {
      throw new Error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 选择配送方式
const selectDelivery = (type: string) => {
  selectedDelivery.value = type
}

// 选择地址
const selectAddress = () => {
  // 跳转到地址选择页面
  uni.navigateTo({
    url: '/addon/yz_she/pages/address/index'
  })
}

// 生成日期选项
const generateDateOptions = () => {
  const today = new Date()
  const options = []

  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    let label = ''
    if (i === 0) {
      label = '今天'
    } else if (i === 1) {
      label = '明天'
    } else if (i === 2) {
      label = '后天'
    } else {
      label = `${date.getMonth() + 1}月${date.getDate()}日`
    }

    options.push({
      date: date,
      label: label,
      value: `${date.getMonth() + 1}月${date.getDate()}日`
    })
  }

  dateOptions.value = options
  generateTimeSlots(0) // 默认选择今天
}

// 生成时间段选项
const generateTimeSlots = (dateIndex: number) => {
  const selectedDate = dateOptions.value[dateIndex]
  const now = new Date()
  const isToday = dateIndex === 0

  const timeSlots = [
    { label: '尽快上门', value: '尽快上门', isUrgent: true },
    { label: '09:00-12:00', value: '09:00-12:00', desc: '上午' },
    { label: '14:00-18:00', value: '14:00-18:00', desc: '下午' },
    { label: '19:00-21:00', value: '19:00-21:00', desc: '晚上' }
  ]

  // 如果是今天，需要判断时间段是否已过
  if (isToday) {
    const currentHour = now.getHours()
    timeSlots.forEach(slot => {
      if (slot.value === '09:00-12:00' && currentHour >= 12) {
        slot.disabled = true
      } else if (slot.value === '14:00-18:00' && currentHour >= 18) {
        slot.disabled = true
      } else if (slot.value === '19:00-21:00' && currentHour >= 21) {
        slot.disabled = true
      }
    })
  }

  currentTimeSlots.value = timeSlots
}

// 显示时间选择弹窗
const showTimeModal = () => {
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择地址',
      icon: 'none'
    })
    return
  }
  showTime.value = true
  generateDateOptions()
}

// 隐藏时间选择弹窗
const hideTimeModal = () => {
  showTime.value = false
  selectedDateIndex.value = 0
  selectedTimeIndex.value = -1
}

// 选择日期
const selectDate = (index: number) => {
  selectedDateIndex.value = index
  selectedTimeIndex.value = -1
  generateTimeSlots(index)
}

// 选择时间段
const selectTimeSlot = (index: number) => {
  if (currentTimeSlots.value[index]?.disabled) return
  selectedTimeIndex.value = index
}

// 确认时间选择
const confirmTime = () => {
  if (!canConfirm.value) return

  const selectedDate = dateOptions.value[selectedDateIndex.value]
  const selectedSlot = currentTimeSlots.value[selectedTimeIndex.value]

  if (selectedSlot.isUrgent) {
    selectedTime.value = '尽快上门'
  } else {
    selectedTime.value = `${selectedDate.value} ${selectedSlot.value}`
  }

  hideTimeModal()

  uni.showToast({
    title: `已选择${selectedTime.value}`,
    icon: 'success'
  })
}

// 显示快递公司选择弹窗
const showExpressModal = () => {
  showExpressSelect.value = true
}

// 隐藏快递公司选择弹窗
const hideExpressModal = () => {
  showExpressSelect.value = false
}

// 选择快递公司
const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
  uni.showToast({
    title: `已选择${express}`,
    icon: 'success'
  })
}

// 复制地址
const copyAddress = () => {
  const address = '四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递'
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    }
  })
}

// 确认批量发货
const confirmBatchShipping = async () => {
  if (submitting.value) return

  try {
    // 验证必填信息
    if (selectedDelivery.value === 'pickup') {
      if (!selectedAddress.value) {
        uni.showToast({
          title: '请选择取件地址',
          icon: 'none'
        })
        return
      }
    } else if (selectedDelivery.value === 'self') {
      if (!selectedExpress.value) {
        uni.showToast({
          title: '请选择快递公司',
          icon: 'none'
        })
        return
      }
      if (!trackingNumber.value.trim()) {
        uni.showToast({
          title: '请输入快递单号',
          icon: 'none'
        })
        return
      }
    }

    submitting.value = true

    // 构建批量发货数据
    const batchData = {
      order_ids: orderList.value.map(order => order.id),
      delivery_type: selectedDelivery.value === 'pickup' ? 1 : 2,
      pickup_address_id: selectedAddress.value?.id || null,
      pickup_time: selectedTime.value,
      express_company: selectedExpress.value,
      express_number: trackingNumber.value
    }

    console.log('批量发货数据:', batchData)

    // 调用批量发货API
    const response = await batchShipQuoteOrders(batchData)

    console.log('批量发货API响应:', response)

    submitting.value = false

    if (response.code === 1) {
      const result = response.data
      const successMsg = `批量发货成功！成功：${result.success_count}个，总计：${result.total_count}个`

      uni.showToast({
        title: successMsg,
        icon: 'success',
        duration: 2000
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    } else {
      throw new Error(response.msg || '批量发货失败')
    }

  } catch (error) {
    submitting.value = false
    console.error('批量发货失败:', error)
    uni.showToast({
      title: '发货失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.batch-order-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 导航栏
.nav-bar {
  background-color: #fff;
  padding-top: var(--status-bar-height);

  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;

    .nav-left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .nav-right {
      width: 60rpx;
    }
  }
}

// 主要内容
.main-content {
  padding: 20rpx 24rpx;
}

// 批量订单概览
.batch-overview {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .overview-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .overview-count {
      font-size: 28rpx;
      color: #666;
    }
  }

  .overview-stats {
    display: flex;
    align-items: center;

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-label {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .stat-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #1abc9c;
      }
    }

    .stat-divider {
      width: 2rpx;
      height: 60rpx;
      background-color: #eee;
      margin: 0 40rpx;
    }
  }
}

// 订单列表部分
.order-list-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);

  .section-header {
    padding: 32rpx 32rpx 0;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .order-list {
    padding: 20rpx 32rpx 32rpx;

    .order-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .order-content {
        display: flex;
        align-items: center;

        .product-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 12rpx;
          margin-right: 24rpx;
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            display: block;
          }

          .order-no {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 12rpx;
            display: block;
          }

          .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .price-label {
              font-size: 24rpx;
              color: #666;
            }

            .price-value {
              font-size: 32rpx;
              font-weight: 600;
              color: #1abc9c;
            }
          }
        }
      }
    }
  }
}

// 待发货状态样式
.shipping-section {
  margin: 20rpx 0;

  // 配送方式
  .delivery-section {
    background-color: #fff;
    margin: 20rpx 24rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .delivery-options {
      display: flex;
      background-color: #f5f5f5;
      border-radius: 12rpx;
      padding: 8rpx;
      margin: 24rpx;

      .delivery-option {
        flex: 1;
        position: relative;
        padding: 20rpx 24rpx;
        background-color: transparent;
        border-radius: 8rpx;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;

        &:active {
          transform: scale(0.98);
        }

        &.active {
          background-color: #fff;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

          .option-name {
            color: #333;
            font-weight: 600;
          }

          .option-tag {
            .tag-text {
              background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
              color: #fff;
              box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.3);
            }
          }
        }

        .option-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;

          .option-name {
            font-size: 26rpx;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
          }

          .option-tag {
            .tag-text {
              font-size: 20rpx;
              color: #fff;
              background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
              padding: 4rpx 12rpx;
              border-radius: 12rpx;
              font-weight: 600;
              transition: all 0.3s ease;
              box-shadow: 0 2rpx 6rpx rgba(22, 160, 133, 0.3);
            }
          }
        }
      }
    }

    .pickup-content, .self-content {
      .address-item, .time-item, .express-item, .tracking-item {
        display: flex;
        align-items: center;
        padding: 32rpx 24rpx;
        border-bottom: 1rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active:not(.disabled) {
          background-color: #f8f9fa;
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .address-icon, .time-icon, .express-icon, .tracking-icon {
          margin-right: 20rpx;
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 32rpx;
            height: 32rpx;
          }

          &.orange-bg {
            background-color: #16a085;
            border-radius: 50%;

            .address-text-icon {
              color: #fff;
              font-size: 24rpx;
              font-weight: 600;
            }
          }
        }

        .address-text, .time-text, .express-text, .tracking-text {
          flex: 1;
          font-size: 30rpx;
          color: #212529;
          font-weight: 500;
        }

        .address-action, .time-action, .express-action {
          font-size: 26rpx;
          color: #6c757d;
          font-weight: 500;

          &.disabled {
            color: #999;
          }
        }

        .tracking-input {
          flex: 1;
          font-size: 26rpx;
          color: #333;
          text-align: right;
          background: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: #999;
          }
        }

        .address-content {
          flex: 1;
          margin-right: 20rpx;

          .selected-address {
            .address-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 600;
              margin-bottom: 8rpx;
              display: block;
            }

            .address-detail {
              font-size: 24rpx;
              color: #666;
              line-height: 1.4;
              display: block;
            }
          }
        }

        .divider-line {
          width: 1rpx;
          height: 32rpx;
          background-color: #e9ecef;
          margin: 0 16rpx;
        }

        .address-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .address-name {
            font-size: 30rpx;
            color: #212529;
            font-weight: 500;
          }

          .address-detail {
            font-size: 24rpx;
            color: #6c757d;
            line-height: 1.4;
          }
        }

        .copy-btn {
          background-color: #16a085;
          color: #fff;
          padding: 12rpx 24rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 500;
        }
      }
    }
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .batch-submit-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.submitting {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    }

    .button-content {
      display: flex;
      align-items: center;

      .normal-content {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .button-icon {
          width: 32rpx;
          height: 32rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-text {
            font-size: 20rpx;
            color: #fff;
            font-weight: 600;
          }
        }

        .button-text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
        }
      }

      .loading-content {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .loading-spinner {
          width: 32rpx;
          height: 32rpx;
          border: 3rpx solid rgba(255, 255, 255, 0.3);
          border-top: 3rpx solid #fff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 快递公司选择弹窗
.express-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .modal-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .express-list {
      max-height: 60vh;
      overflow-y: auto;

      .express-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f8f8f8;
        transition: background-color 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: #f8f9fa;
        }

        .express-name {
          font-size: 28rpx;
          color: #333;
        }

        .express-check {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 600;
        }
      }
    }
  }
}

// 时间选择弹窗样式
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .time-modal-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;

    .time-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .time-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #999;
        background-color: #f8f9fa;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.95);
        }
      }
    }

    .time-picker-container {
      display: flex;
      min-height: 500rpx;

      // 左侧日期列表
      .date-list {
        width: 240rpx;
        background-color: #f8f9fa;
        border-right: 1rpx solid #e9ecef;

        .date-item {
          padding: 32rpx 20rpx;
          border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);
          text-align: center;
          transition: all 0.3s ease;
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &:active {
            background-color: rgba(22, 160, 133, 0.1);
          }

          &.active {
            background-color: #16a085;
            color: #fff;

            .date-text {
              color: #fff;
            }

            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4rpx;
              height: 32rpx;
              background-color: #fff;
              border-radius: 2rpx 0 0 2rpx;
            }
          }

          .date-text {
            font-size: 26rpx;
            font-weight: 500;
            color: #333;
            line-height: 1.4;
          }
        }
      }

      // 右侧时间列表
      .time-list {
        flex: 1;
        padding: 24rpx 20rpx;
        overflow-y: auto;
        background-color: #fff;

        .time-item {
          padding: 24rpx 20rpx;
          border-bottom: 1rpx solid #f0f0f0;
          transition: all 0.3s ease;
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &:active:not(.disabled) {
            background-color: #f8f9fa;
          }

          &.active {
            background-color: #f0fffe;

            .time-text {
              color: #16a085;
              font-weight: 600;
            }

            .time-desc {
              color: #16a085;
            }

            &::after {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4rpx;
              height: 32rpx;
              background-color: #16a085;
              border-radius: 0 2rpx 2rpx 0;
            }
          }

          &.disabled {
            opacity: 0.4;
            pointer-events: none;
            background-color: #f8f9fa;

            .time-text {
              color: #adb5bd;
              text-decoration: line-through;
            }
          }

          &.urgent {
            .time-text {
              color: #333;
              font-weight: 500;
            }

            .time-desc {
              color: #666;
            }

            &.active {
              background-color: #f0fffe;

              .time-text {
                color: #16a085;
                font-weight: 600;
              }

              .time-desc {
                color: #16a085;
              }

              &::after {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4rpx;
                height: 32rpx;
                background-color: #16a085;
                border-radius: 0 2rpx 2rpx 0;
              }
            }
          }

          .time-text {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
          }

          .time-desc {
            font-size: 22rpx;
            color: #666;
            margin-top: 4rpx;
          }
        }
      }
    }

    .time-confirm {
      padding: 24rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;
      background-color: #fff;

      .confirm-btn {
        width: 100%;
        background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
        color: #fff;
        padding: 24rpx 0;
        border-radius: 32rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 16rpx rgba(22, 160, 133, 0.3);

        &:active:not(.disabled) {
          transform: scale(0.98);
          background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);
          box-shadow: 0 2rpx 12rpx rgba(22, 160, 133, 0.5);
        }

        &.disabled {
          background: #e9ecef;
          color: #6c757d;
          box-shadow: none;
          pointer-events: none;
        }

        .confirm-text {
          color: inherit;
          letter-spacing: 1rpx;
        }
      }
    }
  }
}
</style>