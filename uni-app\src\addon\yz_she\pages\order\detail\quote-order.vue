<template>
  <view class="batch-order-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <text class="nav-title">批量发货</text>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 批量订单概览 -->
      <view class="batch-overview">
        <view class="overview-header">
          <text class="overview-title">批量发货订单</text>
          <text class="overview-count">共{{ orderList.length }}个订单</text>
        </view>

        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-label">总金额</text>
            <text class="stat-value">¥{{ totalAmount }}</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-label">预计收益</text>
            <text class="stat-value">¥{{ totalAmount }}</text>
          </view>
        </view>
      </view>

      <!-- 订单列表 -->
      <view class="order-list-section">
        <view class="section-header">
          <text class="section-title">发货订单列表</text>
        </view>

        <view class="order-list">
          <view
            v-for="(order, index) in orderList"
            :key="order.id"
            class="order-item"
          >
            <view class="order-content">
              <image
                class="product-image"
                :src="img(order.product_image)"
                mode="aspectFill"
              />
              <view class="product-info">
                <text class="product-name">{{ order.product_name }}</text>
                <text class="order-no">订单号：{{ order.order_no }}</text>
                <view class="price-info">
                  <text class="price-label">成交价格</text>
                  <text class="price-value">¥{{ order.quote_price }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 配送方式选择 -->
      <view class="delivery-section">
        <view class="section-header">
          <text class="section-title">配送方式</text>
        </view>

        <view class="delivery-options">
          <view
            class="delivery-option"
            :class="{ active: selectedDelivery === 'pickup' }"
            @click="selectDelivery('pickup')"
          >
            <view class="option-content">
              <text class="option-name">快递上门</text>
              <view class="option-tag">
                <text class="tag-text">免费</text>
              </view>
            </view>
          </view>
          <view
            class="delivery-option"
            :class="{ active: selectedDelivery === 'self' }"
            @click="selectDelivery('self')"
          >
            <text class="option-name">自行寄出</text>
          </view>
        </view>

        <!-- 快递上门内容 -->
        <view v-if="selectedDelivery === 'pickup'" class="pickup-content">
          <!-- 地址选择 -->
          <view class="address-item" @click="selectAddress">
            <view class="address-icon">
              <u-icon name="map" color="#333" size="18"></u-icon>
            </view>
            <view class="address-content">
              <text class="address-text" v-if="!selectedAddress">请选择取件地址</text>
              <view v-else class="selected-address">
                <text class="address-name">{{ selectedAddress.name }} {{ selectedAddress.mobile }}</text>
                <text class="address-detail">{{ selectedAddress.full_address }}</text>
              </view>
            </view>
            <view class="divider-line"></view>
            <text class="address-action">地址簿</text>
          </view>

          <!-- 预约时间 -->
          <view class="time-item" :class="{ disabled: !selectedAddress }" @click="showTimeModal">
            <view class="time-icon">
              <svg viewBox="0 0 1024 1024" width="32" height="32">
                <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z" :fill="selectedAddress ? '#333' : '#ccc'"/>
              </svg>
            </view>
            <text class="time-text">期望上门时间</text>
            <text class="time-action" :class="{ disabled: !selectedAddress }">
              {{ !selectedAddress ? '请先选择地址' : (selectedTime || '尽快上门') }} >
            </text>
          </view>
        </view>

        <!-- 自行寄出内容 -->
        <view v-if="selectedDelivery === 'self'" class="self-content">
          <!-- 收货地址 -->
          <view class="address-item">
            <view class="address-icon orange-bg">
              <text class="address-text-icon">收</text>
            </view>
            <view class="address-info">
              <text class="address-name">放心星仓库 13060000687</text>
              <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
            </view>
            <text class="copy-btn" @click="copyAddress">复制</text>
          </view>

          <!-- 快递公司 -->
          <view class="express-item" @click="showExpressModal">
            <view class="express-icon">
              <u-icon name="car" color="#333" size="32"></u-icon>
            </view>
            <text class="express-text">快递公司</text>
            <text class="express-action">{{ selectedExpress || '请选择快递公司' }} ></text>
          </view>

          <!-- 快递单号 -->
          <view class="tracking-item">
            <view class="tracking-icon">
              <u-icon name="order" color="#333" size="32"></u-icon>
            </view>
            <text class="tracking-text">快递单号</text>
            <input
              class="tracking-input"
              v-model="trackingNumber"
              placeholder="请输入快递单号"
              maxlength="30"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="batch-submit-button" :class="{ submitting: submitting }" @click="confirmBatchShipping">
        <view class="button-content">
          <view v-if="!submitting" class="normal-content">
            <view class="button-icon">
              <text class="icon-text">✓</text>
            </view>
            <text class="button-text">确认批量发货</text>
          </view>
          <view v-else class="loading-content">
            <view class="loading-spinner"></view>
            <text class="loading-text">提交中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <u-popup v-model="showExpressSelect" mode="bottom" border-radius="20" :mask-close-able="true">
      <view class="express-popup">
        <view class="popup-header">
          <text class="popup-title">选择快递公司</text>
          <text class="popup-close" @click="showExpressSelect = false">×</text>
        </view>
        <view class="express-list">
          <view
            v-for="(express, index) in expressList"
            :key="index"
            class="express-option"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view v-if="selectedExpress === express" class="express-check">
              <u-icon name="checkmark" color="#1abc9c" size="16"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 时间选择弹窗 -->
    <u-popup v-model="showTime" mode="bottom" border-radius="20" :mask-close-able="true">
      <view class="time-popup">
        <view class="popup-header">
          <text class="popup-title">选择上门时间</text>
          <text class="popup-close" @click="showTime = false">×</text>
        </view>
        <view class="time-content">
          <text class="time-desc">请选择期望的上门取件时间</text>
          <view class="time-options">
            <view
              v-for="(timeOption, index) in timeOptions"
              :key="index"
              class="time-option"
              :class="{ active: selectedTime === timeOption }"
              @click="selectTime(timeOption)"
            >
              <text class="time-option-text">{{ timeOption }}</text>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { img } from '@/utils/common'
import { getBatchQuoteOrderDetails, batchShipQuoteOrders } from '@/addon/yz_she/api/quote'

// 页面参数
const pageParams = ref<any>({})

// 订单列表数据
const orderList = ref<any[]>([])

// 配送方式相关
const selectedDelivery = ref<string>('pickup') // 'pickup' | 'self'
const selectedAddress = ref<any>(null)
const selectedTime = ref<string>('尽快上门')
const selectedExpress = ref<string>('') // 选中的快递公司
const trackingNumber = ref<string>('') // 快递单号
const showExpressSelect = ref<boolean>(false) // 控制快递公司选择弹窗
const showTime = ref<boolean>(false) // 控制时间选择弹窗
const submitting = ref<boolean>(false) // 提交状态

// 快递公司列表
const expressList = [
  '顺丰速运',
  '圆通速递',
  '中通快递',
  '申通快递',
  '韵达速递',
  '百世快递',
  '德邦快递',
  '京东快递',
  '邮政EMS',
  '极兔速递'
]

// 时间选项
const timeOptions = [
  '尽快上门',
  '今天 09:00-12:00',
  '今天 14:00-18:00',
  '明天 09:00-12:00',
  '明天 14:00-18:00',
  '后天 09:00-12:00',
  '后天 14:00-18:00'
]

// 计算总金额
const totalAmount = computed(() => {
  return orderList.value.reduce((sum, order) => {
    return sum + parseFloat(order.quote_price || 0)
  }, 0).toFixed(2)
})

// 页面加载
onLoad((options) => {
  console.log('批量发货页面参数:', options)
  pageParams.value = options || {}

  // 获取传递的订单ID列表
  if (options.orderIds) {
    const orderIds = JSON.parse(decodeURIComponent(options.orderIds))
    loadOrderList(orderIds)
  }
})

// 加载订单列表
const loadOrderList = async (orderIds: number[]) => {
  try {
    console.log('开始加载订单列表，订单IDs:', orderIds)

    // 调用API获取批量订单详情
    const response = await getBatchQuoteOrderDetails(orderIds)

    console.log('API响应:', response)

    if (response.code === 1) {
      orderList.value = response.data
      console.log('订单列表加载成功:', orderList.value)
    } else {
      throw new Error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 选择配送方式
const selectDelivery = (type: string) => {
  selectedDelivery.value = type
}

// 选择地址
const selectAddress = () => {
  // 跳转到地址选择页面
  uni.navigateTo({
    url: '/addon/yz_she/pages/address/index'
  })
}

// 显示时间选择弹窗
const showTimeModal = () => {
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择地址',
      icon: 'none'
    })
    return
  }
  showTime.value = true
}

// 选择时间
const selectTime = (time: string) => {
  selectedTime.value = time
  showTime.value = false
}

// 显示快递公司选择弹窗
const showExpressModal = () => {
  showExpressSelect.value = true
}

// 选择快递公司
const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
}

// 复制地址
const copyAddress = () => {
  const address = '四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递'
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    }
  })
}

// 确认批量发货
const confirmBatchShipping = async () => {
  if (submitting.value) return

  try {
    // 验证必填信息
    if (selectedDelivery.value === 'pickup') {
      if (!selectedAddress.value) {
        uni.showToast({
          title: '请选择取件地址',
          icon: 'none'
        })
        return
      }
    } else if (selectedDelivery.value === 'self') {
      if (!selectedExpress.value) {
        uni.showToast({
          title: '请选择快递公司',
          icon: 'none'
        })
        return
      }
      if (!trackingNumber.value.trim()) {
        uni.showToast({
          title: '请输入快递单号',
          icon: 'none'
        })
        return
      }
    }

    submitting.value = true

    // 构建批量发货数据
    const batchData = {
      order_ids: orderList.value.map(order => order.id),
      delivery_type: selectedDelivery.value === 'pickup' ? 1 : 2,
      pickup_address_id: selectedAddress.value?.id || null,
      pickup_time: selectedTime.value,
      express_company: selectedExpress.value,
      express_number: trackingNumber.value
    }

    console.log('批量发货数据:', batchData)

    // 调用批量发货API
    const response = await batchShipQuoteOrders(batchData)

    console.log('批量发货API响应:', response)

    submitting.value = false

    if (response.code === 1) {
      const result = response.data
      const successMsg = `批量发货成功！成功：${result.success_count}个，总计：${result.total_count}个`

      uni.showToast({
        title: successMsg,
        icon: 'success',
        duration: 2000
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    } else {
      throw new Error(response.msg || '批量发货失败')
    }

  } catch (error) {
    submitting.value = false
    console.error('批量发货失败:', error)
    uni.showToast({
      title: '发货失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.batch-order-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 导航栏
.nav-bar {
  background-color: #fff;
  padding-top: var(--status-bar-height);

  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;

    .nav-left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .nav-right {
      width: 60rpx;
    }
  }
}

// 主要内容
.main-content {
  padding: 20rpx 24rpx;
}

// 批量订单概览
.batch-overview {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .overview-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .overview-count {
      font-size: 28rpx;
      color: #666;
    }
  }

  .overview-stats {
    display: flex;
    align-items: center;

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-label {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .stat-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #1abc9c;
      }
    }

    .stat-divider {
      width: 2rpx;
      height: 60rpx;
      background-color: #eee;
      margin: 0 40rpx;
    }
  }
}

// 订单列表部分
.order-list-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);

  .section-header {
    padding: 32rpx 32rpx 0;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .order-list {
    padding: 20rpx 32rpx 32rpx;

    .order-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .order-content {
        display: flex;
        align-items: center;

        .product-image {
          width: 120rpx;
          height: 120rpx;
          border-radius: 12rpx;
          margin-right: 24rpx;
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            display: block;
          }

          .order-no {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 12rpx;
            display: block;
          }

          .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .price-label {
              font-size: 24rpx;
              color: #666;
            }

            .price-value {
              font-size: 32rpx;
              font-weight: 600;
              color: #1abc9c;
            }
          }
        }
      }
    }
  }
}

// 配送方式部分
.delivery-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);

  .section-header {
    padding: 32rpx 32rpx 0;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .delivery-options {
    display: flex;
    padding: 20rpx 32rpx;
    gap: 20rpx;

    .delivery-option {
      flex: 1;
      height: 80rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &.active {
        border-color: #1abc9c;
        background-color: rgba(26, 188, 156, 0.1);

        .option-name {
          color: #1abc9c;
          font-weight: 600;
        }

        .option-tag {
          background-color: #1abc9c;
        }
      }

      .option-content {
        display: flex;
        align-items: center;
        gap: 12rpx;
      }

      .option-name {
        font-size: 28rpx;
        color: #333;
      }

      .option-tag {
        background-color: #ff6b6b;
        color: #fff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 8rpx;

        .tag-text {
          font-size: 20rpx;
        }
      }
    }
  }

  // 快递上门内容
  .pickup-content {
    padding: 0 32rpx 32rpx;

    .address-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      .address-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 24rpx;
      }

      .address-content {
        flex: 1;

        .address-text {
          font-size: 28rpx;
          color: #999;
        }

        .selected-address {
          .address-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            display: block;
            margin-bottom: 8rpx;
          }

          .address-detail {
            font-size: 24rpx;
            color: #666;
            display: block;
          }
        }
      }

      .divider-line {
        width: 2rpx;
        height: 40rpx;
        background-color: #e0e0e0;
        margin: 0 20rpx;
      }

      .address-action {
        font-size: 28rpx;
        color: #1abc9c;
      }
    }

    .time-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;

      &.disabled {
        opacity: 0.5;
      }

      .time-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 24rpx;
      }

      .time-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }

      .time-action {
        font-size: 28rpx;
        color: #1abc9c;

        &.disabled {
          color: #ccc;
        }
      }
    }
  }

  // 自行寄出内容
  .self-content {
    padding: 0 32rpx 32rpx;

    .address-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      .address-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        &.orange-bg {
          background-color: #ff9500;
        }

        .address-text-icon {
          font-size: 24rpx;
          color: #fff;
          font-weight: 600;
        }
      }

      .address-info {
        flex: 1;

        .address-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          display: block;
          margin-bottom: 8rpx;
        }

        .address-detail {
          font-size: 24rpx;
          color: #666;
          display: block;
        }
      }

      .copy-btn {
        font-size: 28rpx;
        color: #1abc9c;
        padding: 8rpx 16rpx;
        border: 1rpx solid #1abc9c;
        border-radius: 8rpx;
      }
    }

    .express-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      .express-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 24rpx;
      }

      .express-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }

      .express-action {
        font-size: 28rpx;
        color: #1abc9c;
      }
    }

    .tracking-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;

      .tracking-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 24rpx;
      }

      .tracking-text {
        width: 120rpx;
        font-size: 28rpx;
        color: #333;
      }

      .tracking-input {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        text-align: right;
      }
    }
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .batch-submit-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.submitting {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    }

    .button-content {
      display: flex;
      align-items: center;

      .normal-content {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .button-icon {
          width: 32rpx;
          height: 32rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-text {
            font-size: 20rpx;
            color: #fff;
            font-weight: 600;
          }
        }

        .button-text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
        }
      }

      .loading-content {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .loading-spinner {
          width: 32rpx;
          height: 32rpx;
          border: 3rpx solid rgba(255, 255, 255, 0.3);
          border-top: 3rpx solid #fff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text {
          font-size: 32rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 弹窗样式
.express-popup, .time-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .popup-close {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
}

.express-popup {
  .express-list {
    max-height: 60vh;
    overflow-y: auto;

    .express-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 32rpx;
      border-bottom: 1rpx solid #f8f8f8;

      &:last-child {
        border-bottom: none;
      }

      .express-name {
        font-size: 28rpx;
        color: #333;
      }

      .express-check {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.time-popup {
  .time-content {
    padding: 32rpx;

    .time-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 32rpx;
      display: block;
    }

    .time-options {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .time-option {
        padding: 20rpx 24rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        transition: all 0.3s ease;

        &.active {
          border-color: #1abc9c;
          background-color: rgba(26, 188, 156, 0.1);

          .time-option-text {
            color: #1abc9c;
            font-weight: 600;
          }
        }

        .time-option-text {
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}
</style>