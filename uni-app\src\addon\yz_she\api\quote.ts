import request from '@/utils/request'

// 估价订单接口参数类型
export interface QuoteOrderCreateParams {
  category_id: number
  brand_id?: number
  product_id?: number
  product_name?: string
  product_code?: string
  product_image?: string
  photos: Record<string, string>
  defect_photos?: string[]
  accessories?: any[]
  note?: string
}

export interface QuoteOrderListParams {
  order_no?: string
  status?: number | string
  category_id?: number
  brand_id?: number
  page?: number
  limit?: number
}

/**
 * 创建估价订单
 */
export function createQuoteOrder(params: QuoteOrderCreateParams) {
  return request.post('yz_she/quote/order', params)
}

/**
 * 获取估价订单列表
 */
export function getQuoteOrderList(params: QuoteOrderListParams) {
  // 构建干净的参数对象，确保所有值都是有效的
  const queryParams: any = {}

  if (params.order_no && params.order_no.trim()) {
    queryParams.order_no = params.order_no.trim()
  }

  if (params.status !== undefined && params.status !== null && params.status !== '') {
    queryParams.status = Number(params.status)
  }

  if (params.category_id && params.category_id > 0) {
    queryParams.category_id = Number(params.category_id)
  }

  if (params.brand_id && params.brand_id > 0) {
    queryParams.brand_id = Number(params.brand_id)
  }

  if (params.page && params.page > 0) {
    queryParams.page = Number(params.page)
  }

  if (params.limit && params.limit > 0) {
    queryParams.limit = Number(params.limit)
  }

  return request.get('yz_she/quote/order/list', queryParams)
}

/**
 * 获取估价订单详情
 */
export function getQuoteOrderDetail(id: number) {
  return request.get(`yz_she/quote/order/${id}`)
}

/**
 * 确认估价
 */
export function confirmQuoteOrder(id: number) {
  return request.post(`yz_she/quote/order/${id}/confirm`)
}

/**
 * 取消订单
 */
export function cancelQuoteOrder(id: number, reason?: string) {
  return request.post(`yz_she/quote/order/${id}/cancel`, { reason })
}

/**
 * 更新估价订单状态
 */
export function updateQuoteOrderStatus(id: number, status: number) {
  return request.post(`yz_she/quote/order/${id}/status`, { status })
}

/**
 * 获取订单状态列表
 */
export function getQuoteOrderStatus() {
  return request.get('yz_she/quote/order/status')
}

/**
 * 批量获取订单详情
 */
export function getBatchQuoteOrderDetails(orderIds: number[]) {
  return request.post('yz_she/quote/order/batch/detail', { order_ids: orderIds })
}

/**
 * 批量发货
 */
export interface BatchShipParams {
  order_ids: number[]
  delivery_type: number // 1=快递上门, 2=用户自寄
  pickup_address_id?: number
  pickup_time?: string
  express_company?: string
  express_number?: string
  ship_note?: string
}

export function batchShipQuoteOrders(params: BatchShipParams) {
  return request.post('yz_she/quote/order/batch/ship', params)
}
