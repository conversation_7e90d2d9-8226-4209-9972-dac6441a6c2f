<template>
  <view class="quote-detail-page">
    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content" v-else>
      <!-- 商品信息卡片（融合订单信息和评估图片） -->
      <view class="product-card">
        <!-- 待发货状态的顶部提示栏 -->
        <view v-if="orderInfo.status === 3" class="shipping-notice">
          <text class="notice-text">单次估价有效时间，请在{{ formatValidTime(orderInfo.quote_time) }}前寄出，超时可能导致订单失效</text>
          <text class="notice-right">限时有效</text>
        </view>

        <!-- 商品基本信息 -->
        <view class="product-info">
          <image
            v-if="orderInfo.product_image"
            :src="img(orderInfo.product_image)"
            class="product-image"
            mode="aspectFit"
          ></image>
          <view
            v-else
            class="product-image no-image"
          >
            <u-icon name="image" color="#ccc" size="40"></u-icon>
          </view>
          <view class="product-details">
            <text class="product-name">{{ orderInfo.product_name || '商品名称' }}</text>
            <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
            <text class="order-no">估价订单号：{{ orderInfo.order_no }}</text>
          </view>
        </view>

        <!-- 订单状态和价格 - 非已完成状态显示订单状态 -->
        <view class="status-section">
          <view class="status-info" v-if="orderInfo.status !== 4">
            <text class="status-label">订单状态</text>
            <view class="status-badge" :class="getStatusClass(orderInfo.status)">
              <text class="status-text">{{ getStatusText(orderInfo.status) }}</text>
            </view>
          </view>

          <!-- 价格信息 -->
          <view class="price-info" v-if="orderInfo.quote_price">
            <text class="price-label">估价金额</text>
            <view class="price-amount">
              <text class="currency">¥</text>
              <text class="price-value">{{ orderInfo.quote_price }}</text>
            </view>
          </view>
          <view class="price-info" v-else>
            <text class="price-label">估价金额</text>
            <text class="price-pending">估价中...</text>
          </view>
        </view>

        <!-- 订单时间信息 - 非已完成状态显示 -->
        <view class="time-info">
          <view class="time-item">
            <text class="time-label">创建时间</text>
            <text class="time-value">{{ formatTime(orderInfo.create_time) }}</text>
          </view>
          <view class="time-item" v-if="orderInfo.quote_time">
            <text class="time-label">估价时间</text>
            <text class="time-value">{{ formatTime(orderInfo.quote_time) }}</text>
          </view>
        </view>

        <!-- 用户上传的评估图片（融合在商品卡片中） -->
        <view class="upload-photos" v-if="userPhotos.length > 0">
          <text class="photos-title">评估图片</text>
          <scroll-view class="photos-scroll" scroll-x="true" show-scrollbar="false">
            <view class="photos-list">
              <view
                class="photo-item"
                v-for="photo in userPhotos"
                :key="photo.id"
              >
                <image
                  :src="img(photo.photo_url)"
                  class="photo-image"
                  mode="aspectFill"
                  @click="previewPhoto(photo.photo_url)"
                ></image>
                <text class="photo-label">{{ photo.photo_name }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 选择的配件（并排显示） - 非已完成状态且非待发货状态显示 -->
      <view class="accessories-card" v-if="orderAccessories.length > 0 && orderInfo.status !== 4 && orderInfo.status !== 3">
        <text class="card-title">选择配件</text>
        <view class="accessories-list">
          <view
            class="accessory-tag"
            v-for="accessory in orderAccessories"
            :key="accessory.id"
          >
            <text class="accessory-name">{{ accessory.accessory_name }}</text>
          </view>
        </view>
      </view>

      <!-- 估价说明 - 非已完成状态且非待发货状态显示 -->
      <view class="quote-note-card" v-if="quoteNote && quoteNote.trim() && orderInfo.status !== 4 && orderInfo.status !== 3">
        <text class="card-title">估价说明</text>
        <view class="note-content">
          <text class="note-text">{{ quoteNote }}</text>
        </view>
      </view>

      <!-- 用户备注 - 非已完成状态且非待发货状态显示 -->
      <view class="user-note-card" v-if="orderInfo.user_note && orderInfo.status !== 4 && orderInfo.status !== 3">
        <text class="card-title">用户备注</text>
        <view class="note-content">
          <text class="note-text">{{ orderInfo.user_note }}</text>
        </view>
      </view>

      <!-- 待发货状态的配送方式选择 -->
      <view v-if="orderInfo.status === 3" class="shipping-section">



        <!-- 配送方式 -->
        <view class="delivery-section">
          <view class="delivery-options">
            <view
              class="delivery-option"
              :class="{ active: selectedDelivery === 'pickup' }"
              @click="selectDelivery('pickup')"
            >
              <view class="option-content">
                <text class="option-name">快递上门</text>
                <view class="option-tag">
                  <text class="tag-text">免费</text>
                </view>
              </view>
            </view>
            <view
              class="delivery-option"
              :class="{ active: selectedDelivery === 'self' }"
              @click="selectDelivery('self')"
            >
              <text class="option-name">自行寄出</text>
            </view>
          </view>

          <!-- 快递上门内容 -->
          <view v-if="selectedDelivery === 'pickup'" class="pickup-content">
            <!-- 地址选择 -->
            <view class="address-item" @click="selectAddress">
              <view class="address-icon">
                <u-icon name="map" color="#333" size="18"></u-icon>
              </view>
              <view class="address-content">
                <text class="address-text" v-if="!selectedAddress">请选择取件地址</text>
                <view v-else class="selected-address">
                  <text class="address-name">{{ selectedAddress.name }} {{ selectedAddress.mobile }}</text>
                  <text class="address-detail">{{ selectedAddress.full_address }}</text>
                </view>
              </view>
              <view class="divider-line"></view>
              <text class="address-action">地址簿</text>
            </view>

            <!-- 预约时间 -->
            <view class="time-item" :class="{ disabled: !selectedAddress }" @click="showTimeModal">
              <view class="time-icon">
                <svg viewBox="0 0 1024 1024" width="32" height="32">
                  <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z" :fill="selectedAddress ? '#333' : '#ccc'"/>
                </svg>
              </view>
              <text class="time-text">期望上门时间</text>
              <text class="time-action" :class="{ disabled: !selectedAddress }">
                {{ !selectedAddress ? '请先选择地址' : (selectedTime || '尽快上门') }} >
              </text>
            </view>
          </view>

          <!-- 自行寄出内容 -->
          <view v-if="selectedDelivery === 'self'" class="self-content">
            <!-- 收货地址 -->
            <view class="address-item">
              <view class="address-icon orange-bg">
                <text class="address-text-icon">收</text>
              </view>
              <view class="address-info">
                <text class="address-name">放心星仓库 13060000687</text>
                <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
              </view>
              <text class="copy-btn" @click="copyAddress">复制</text>
            </view>

            <!-- 快递公司 -->
            <view class="express-item" @click="showExpressModal">
              <view class="express-icon">
                <u-icon name="car" color="#333" size="32"></u-icon>
              </view>
              <text class="express-text">快递公司</text>
              <text class="express-action">{{ selectedExpress || '请选择快递公司' }} ></text>
            </view>

            <!-- 快递单号 -->
            <view class="tracking-item">
              <view class="tracking-icon">
                <u-icon name="order" color="#333" size="32"></u-icon>
              </view>
              <text class="tracking-text">快递单号</text>
              <input
                class="tracking-input"
                v-model="trackingNumber"
                placeholder="请输入快递单号"
                maxlength="30"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 已完成状态的成功展示区域 -->
      <view class="success-section" v-if="orderInfo.status === 4">
        <view class="success-icon">
          <view class="clipboard-bg">
            <view class="clipboard-body">
              <view class="clipboard-lines">
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
              </view>
            </view>
            <view class="clipboard-clip"></view>
          </view>
          <view class="check-mark">
            <text class="check-icon">✓</text>
          </view>
        </view>

        <text class="success-title">订单创建成功</text>
        <text class="order-number">回收订单号: {{ recycleOrderNo || '生成中...' }}</text>
        <text class="success-desc">您可以在"我的订单"中查看该订单信息</text>

        <!-- 查看订单按钮 -->
        <view class="view-order-button" @click="viewRecycleOrder">
          <text class="button-text">查看订单</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="hasBottomActions">
      <!-- 估价中状态：取消订单 -->
      <view v-if="orderInfo.status === 1" class="single-button">
        <view class="cancel-button" @click="showCancelDialog">
          <text class="button-text">取消订单</text>
        </view>
      </view>

      <!-- 待确认状态：取消订单、确认回收 -->
      <view v-if="orderInfo.status === 2" class="double-buttons">
        <view class="cancel-button secondary" @click="showCancelDialog">
          <text class="button-text">取消订单</text>
        </view>
        <view class="confirm-button primary" @click="confirmRecycle">
          <text class="button-text">确认回收</text>
        </view>
      </view>

      <!-- 待发货状态：确认回收 -->
      <view v-if="orderInfo.status === 3" class="single-button">
        <view class="confirm-button primary" :class="{ submitting: submitting }" @click="confirmShipping">
          <text class="button-text" v-if="!submitting">确认回收</text>
          <text class="button-text" v-else>提交中...</text>
        </view>
      </view>

      <!-- 已取消状态：重新回收 -->
      <view v-if="orderInfo.status === 5" class="single-button">
        <view class="recycle-button" @click="recycleAgain">
          <text class="button-text">重新回收</text>
        </view>
      </view>
    </view>

    <!-- 取消订单确认弹窗 -->
    <view class="cancel-modal" v-if="showCancel" @click="hideCancelDialog">
      <view class="cancel-modal-content" @click.stop>
        <view class="cancel-header">
          <text class="cancel-title">取消订单</text>
        </view>
        <view class="cancel-body">
          <text class="cancel-message">确定要取消这个估价订单吗？</text>
          <text class="cancel-note">取消后将无法恢复，需要重新提交估价申请。</text>
        </view>
        <view class="cancel-actions">
          <view class="cancel-btn secondary" @click="hideCancelDialog">
            <text class="btn-text">再想想</text>
          </view>
          <view class="cancel-btn primary" @click="confirmCancel">
            <text class="btn-text">确认取消</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <view class="express-modal" v-if="showExpressSelect" @click="hideExpressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择快递公司</text>
          <view class="close-btn" @click="hideExpressModal">×</view>
        </view>

        <view class="express-list">
          <view
            class="express-option"
            v-for="express in expressList"
            :key="express"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view class="express-check" v-if="selectedExpress === express">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择弹窗 -->
    <view class="time-modal" v-if="showTime" @click="hideTimeModal">
      <view class="time-modal-content" @click.stop>
        <view class="time-header">
          <text class="time-title">期望上门时间</text>
          <view class="close-btn" @click="hideTimeModal">×</view>
        </view>

        <!-- 左右分栏布局 -->
        <view class="time-container">
          <!-- 左侧日期选择 -->
          <view class="date-sidebar">
            <view
              class="date-option"
              :class="{ active: selectedDate === 'today' }"
              @click="selectDate('today')"
            >
              <text class="date-text">今天</text>
              <text class="date-desc">{{ todayDesc }}</text>
            </view>
            <view
              class="date-option"
              :class="{ active: selectedDate === 'tomorrow' }"
              @click="selectDate('tomorrow')"
            >
              <text class="date-text">明天</text>
              <text class="date-desc">{{ tomorrowDesc }}</text>
            </view>
            <view
              class="date-option"
              :class="{ active: selectedDate === 'dayafter' }"
              @click="selectDate('dayafter')"
            >
              <text class="date-text">后天</text>
              <text class="date-desc">{{ dayafterDesc }}</text>
            </view>
          </view>

          <!-- 右侧时间选择 -->
          <view class="time-content">
            <!-- 今天的时间选项 -->
            <view v-if="selectedDate === 'today'" class="time-slots">
              <view
                class="time-slot urgent"
                :class="{ active: selectedTimeSlot === 'today-urgent' }"
                @click="selectTimeSlot('today-urgent')"
              >
                <text class="slot-title">尽快上门</text>
                <text class="slot-desc">工作日2小时内</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-morning' }"
                @click="selectTimeSlot('today-morning')"
              >
                <text class="slot-title">上午</text>
                <text class="slot-desc">9:00-12:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-afternoon' }"
                @click="selectTimeSlot('today-afternoon')"
              >
                <text class="slot-title">下午</text>
                <text class="slot-desc">14:00-18:00</text>
              </view>
            </view>

            <!-- 明天的时间选项 -->
            <view v-if="selectedDate === 'tomorrow'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-morning' }"
                @click="selectTimeSlot('tomorrow-morning')"
              >
                <text class="slot-title">上午</text>
                <text class="slot-desc">9:00-12:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-afternoon' }"
                @click="selectTimeSlot('tomorrow-afternoon')"
              >
                <text class="slot-title">下午</text>
                <text class="slot-desc">14:00-18:00</text>
              </view>
            </view>

            <!-- 后天的时间选项 -->
            <view v-if="selectedDate === 'dayafter'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-morning' }"
                @click="selectTimeSlot('dayafter-morning')"
              >
                <text class="slot-title">上午</text>
                <text class="slot-desc">9:00-12:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-afternoon' }"
                @click="selectTimeSlot('dayafter-afternoon')"
              >
                <text class="slot-title">下午</text>
                <text class="slot-desc">14:00-18:00</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 确认按钮 -->
        <view class="time-confirm">
          <view class="confirm-btn" @click="confirmTime">
            <text class="confirm-text">确认</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderDetail, cancelQuoteOrder } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 订单信息
const orderInfo = ref<any>({
  id: '',
  order_no: '',
  product_name: '',
  product_code: '',
  product_image: '',
  status: 1,
  quote_price: null,
  user_note: '',
  create_time: '',
  quote_time: ''
})

// 用户上传的图片
const userPhotos = ref<any[]>([])

// 订单配件
const orderAccessories = ref<any[]>([])

// 估价说明
const quoteNote = ref<string>('')

// 回收订单号（已完成状态时显示）
const recycleOrderNo = ref<string>('')

// 加载状态
const loading = ref(false)

// 取消订单弹窗
const showCancel = ref(false)
const cancelling = ref(false)

// 配送方式相关（待发货状态使用）
const selectedDelivery = ref<string>('pickup') // 'pickup' | 'self'
const selectedAddress = ref<any>(null)
const selectedTime = ref<string>('尽快上门')
const selectedExpress = ref<string>('') // 选中的快递公司
const trackingNumber = ref<string>('') // 快递单号
const showExpressSelect = ref<boolean>(false) // 控制快递公司选择弹窗
const showTime = ref<boolean>(false) // 控制时间选择弹窗
const selectedDate = ref<string>('today') // 选中的日期
const selectedTimeSlot = ref<string>('today-urgent') // 选中的时间段
const submitting = ref<boolean>(false) // 提交状态

// 日期描述
const todayDesc = ref<string>('')
const tomorrowDesc = ref<string>('')
const dayafterDesc = ref<string>('')

// 快递公司列表
const expressList = [
  '顺丰速运',
  '京东物流',
  '德邦快递',
  '中通快递',
  '韵达速递',
  '圆通速递',
  '申通快递',
  '极兔速递'
]

// 计算属性：是否显示底部操作按钮
const hasBottomActions = computed(() => {
  // 估价中、待确认、待发货、已取消状态显示操作按钮
  return [1, 2, 3, 5].includes(orderInfo.value.status)
})

// 订单状态常量
const ORDER_STATUS = {
  QUOTING: 1,        // 估价中
  PENDING_CONFIRM: 2, // 待确认
  PENDING_SHIP: 3,   // 待发货
  COMPLETED: 4,      // 已完成
  CANCELLED: 5       // 已取消
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    [ORDER_STATUS.QUOTING]: '估价中',
    [ORDER_STATUS.PENDING_CONFIRM]: '待确认',
    [ORDER_STATUS.PENDING_SHIP]: '待发货',
    [ORDER_STATUS.COMPLETED]: '已完成',
    [ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classMap = {
    [ORDER_STATUS.QUOTING]: 'quoting',
    [ORDER_STATUS.PENDING_CONFIRM]: 'pending-confirm',
    [ORDER_STATUS.PENDING_SHIP]: 'pending-ship',
    [ORDER_STATUS.COMPLETED]: 'completed',
    [ORDER_STATUS.CANCELLED]: 'cancelled'
  }
  return classMap[status] || 'default'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 预览图片
const previewPhoto = (photoUrl: string) => {
  const allPhotos = userPhotos.value.map(photo => img(photo.photo_url))
  const currentIndex = allPhotos.indexOf(img(photoUrl))

  uni.previewImage({
    urls: allPhotos,
    current: currentIndex
  })
}

// 显示取消订单弹窗
const showCancelDialog = () => {
  showCancel.value = true
}

// 隐藏取消订单弹窗
const hideCancelDialog = () => {
  showCancel.value = false
}

// 确认取消订单
const confirmCancel = async () => {
  if (cancelling.value) return

  try {
    cancelling.value = true

    await cancelQuoteOrder(orderInfo.value.id, '用户主动取消')

    uni.showToast({
      title: '订单已取消',
      icon: 'success',
      duration: 2000
    })

    // 更新订单状态
    orderInfo.value.status = ORDER_STATUS.CANCELLED

    // 关闭弹窗
    showCancel.value = false

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)

  } catch (error) {
    console.error('取消订单失败:', error)
    uni.showToast({
      title: error.message || '取消失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    cancelling.value = false
  }
}

// 确认回收
const confirmRecycle = async () => {
  try {
    // 这里需要调用确认回收的API
    // await confirmQuoteOrder(orderInfo.value.id)

    uni.showToast({
      title: '确认成功',
      icon: 'success',
      duration: 2000
    })

    // 更新订单状态为待发货
    orderInfo.value.status = ORDER_STATUS.PENDING_SHIP

  } catch (error) {
    console.error('确认回收失败:', error)
    uni.showToast({
      title: error.message || '确认失败，请重试',
      icon: 'none',
      duration: 3000
    })
  }
}

// 重新回收
const recycleAgain = () => {
  // 跳转到品牌选择页面重新开始回收流程
  uni.navigateTo({
    url: '/addon/yz_she/pages/brand/index'
  })
}

// 查看回收订单
const viewRecycleOrder = () => {
  // 跳转到订单列表页面
  uni.navigateTo({
    url: '/addon/yz_she/pages/order/order-list'
  })
}

// 格式化有效时间
const formatValidTime = (quoteTime: string) => {
  if (!quoteTime) return ''
  const quoteDate = new Date(quoteTime)
  // 估价后24小时有效
  const validDate = new Date(quoteDate.getTime() + 24 * 60 * 60 * 1000)
  const month = String(validDate.getMonth() + 1).padStart(2, '0')
  const day = String(validDate.getDate()).padStart(2, '0')
  const hours = String(validDate.getHours()).padStart(2, '0')
  const minutes = String(validDate.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 初始化日期描述
const initDateDescriptions = () => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  const dayafter = new Date(today)
  dayafter.setDate(today.getDate() + 2)

  todayDesc.value = `${today.getMonth() + 1}月${today.getDate()}日`
  tomorrowDesc.value = `${tomorrow.getMonth() + 1}月${tomorrow.getDate()}日`
  dayafterDesc.value = `${dayafter.getMonth() + 1}月${dayafter.getDate()}日`
}

// 选择配送方式
const selectDelivery = (type: string) => {
  selectedDelivery.value = type
  console.log('选择配送方式:', type)
}

// 选择地址
const selectAddress = () => {
  // 模拟地址选择，实际应该跳转到地址选择页面
  const mockAddress = {
    id: 1,
    name: '张三',
    mobile: '138****8888',
    full_address: '北京市朝阳区三里屯街道工体北路8号院',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '三里屯街道工体北路8号院'
  }

  selectedAddress.value = mockAddress
  uni.showToast({
    title: '地址选择成功',
    icon: 'success'
  })
}

// 复制地址
const copyAddress = () => {
  const address = '四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递'
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    }
  })
}

// 时间选择相关方法
const showTimeModal = () => {
  // 检查是否已选择地址
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择取件地址',
      icon: 'none',
      duration: 2000
    })
    return
  }

  showTime.value = true
}

const hideTimeModal = () => {
  showTime.value = false
}

const selectDate = (date: string) => {
  selectedDate.value = date
  // 重置时间段选择
  if (date === 'today') {
    selectedTimeSlot.value = 'today-urgent'
  } else if (date === 'tomorrow') {
    selectedTimeSlot.value = 'tomorrow-morning'
  } else if (date === 'dayafter') {
    selectedTimeSlot.value = 'dayafter-morning'
  }
}

const selectTimeSlot = (slot: string) => {
  selectedTimeSlot.value = slot
}

const confirmTime = () => {
  // 根据选择的日期和时间段生成时间文本
  let timeText = ''

  if (selectedTimeSlot.value === 'today-urgent') {
    timeText = '尽快上门'
  } else if (selectedTimeSlot.value === 'today-morning') {
    timeText = '今天上午(9:00-12:00)'
  } else if (selectedTimeSlot.value === 'today-afternoon') {
    timeText = '今天下午(14:00-18:00)'
  } else if (selectedTimeSlot.value === 'tomorrow-morning') {
    timeText = '明天上午(9:00-12:00)'
  } else if (selectedTimeSlot.value === 'tomorrow-afternoon') {
    timeText = '明天下午(14:00-18:00)'
  } else if (selectedTimeSlot.value === 'dayafter-morning') {
    timeText = '后天上午(9:00-12:00)'
  } else if (selectedTimeSlot.value === 'dayafter-afternoon') {
    timeText = '后天下午(14:00-18:00)'
  }

  selectedTime.value = timeText
  showTime.value = false
  console.log('选择时间:', timeText)
}

// 快递公司选择相关方法
const showExpressModal = () => {
  showExpressSelect.value = true
}

const hideExpressModal = () => {
  showExpressSelect.value = false
}

const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
  uni.showToast({
    title: `已选择${express}`,
    icon: 'success'
  })
}

// 确认回收（待发货状态）
const confirmShipping = () => {
  // 验证必填信息
  if (selectedDelivery.value === 'pickup') {
    if (!selectedAddress.value) {
      uni.showToast({
        title: '请选择取件地址',
        icon: 'none'
      })
      return
    }

    if (!selectedTime.value) {
      uni.showToast({
        title: '请选择上门时间',
        icon: 'none'
      })
      return
    }
  } else if (selectedDelivery.value === 'self') {
    if (!selectedExpress.value) {
      uni.showToast({
        title: '请选择快递公司',
        icon: 'none'
      })
      return
    }

    if (!trackingNumber.value || !trackingNumber.value.trim()) {
      uni.showToast({
        title: '请输入快递单号',
        icon: 'none'
      })
      return
    }
  }

  submitting.value = true

  // 构建回收订单数据
  const recycleOrderData = {
    quote_id: orderInfo.value.id,
    delivery_type: selectedDelivery.value === 'pickup' ? 1 : 2, // 1=快递上门, 2=用户自寄
    address_info: selectedAddress.value,
    pickup_time: selectedTime.value,
    express_company: selectedExpress.value,
    express_number: trackingNumber.value
  }

  console.log('提交回收订单数据:', recycleOrderData)

  // 模拟提交
  setTimeout(() => {
    submitting.value = false
    uni.showToast({
      title: '回收订单提交成功',
      icon: 'success'
    })

    // 更新订单状态为已完成
    orderInfo.value.status = ORDER_STATUS.COMPLETED

    // 生成回收订单号
    recycleOrderNo.value = `RC${Date.now().toString().slice(-8)}`
  }, 2000)
}

// 加载订单详情
const loadOrderDetail = async (orderId: string) => {
  if (!orderId) {
    uni.showToast({
      title: '订单ID不能为空',
      icon: 'none'
    })
    return
  }

  try {
    loading.value = true

    const response = await getQuoteOrderDetail(parseInt(orderId))

    if (response.code === 1) {
      const data = response.data

      // 设置订单基本信息
      orderInfo.value = {
        id: data.id,
        order_no: data.order_no || '',
        product_name: data.product_name || data.brand?.name || '估价商品',
        product_code: data.product_code || '',
        product_image: data.product_image || '',
        status: data.status || ORDER_STATUS.QUOTING,
        quote_price: data.quote_price || null,
        user_note: data.user_note || '',
        create_time: data.create_time || '',
        quote_time: data.quote_time || ''
      }

      // 设置用户上传的图片
      userPhotos.value = (data.photos || []).map(photo => ({
        ...photo,
        photo_url: photo.photo_url
      }))

      // 设置订单配件
      orderAccessories.value = data.accessories || []

      // 设置估价说明（从最新的估价记录中获取）
      console.log('估价记录数据:', data.latestQuoteRecord)
      if (data.latestQuoteRecord && data.latestQuoteRecord.quote_note) {
        quoteNote.value = data.latestQuoteRecord.quote_note
        console.log('设置估价说明:', quoteNote.value)
      } else {
        quoteNote.value = ''
        console.log('没有估价说明数据')
      }

      // 设置回收订单号（已完成状态时）
      if (data.status === ORDER_STATUS.COMPLETED && data.recycle_order_no) {
        recycleOrderNo.value = data.recycle_order_no
      } else if (data.status === ORDER_STATUS.COMPLETED) {
        // 如果是已完成状态但没有回收订单号，生成一个临时的
        recycleOrderNo.value = `RC${data.order_no.substring(2)}`
      }

      // 设置回收订单号（已完成状态时）
      if (data.status === ORDER_STATUS.COMPLETED && data.recycle_order_no) {
        recycleOrderNo.value = data.recycle_order_no
      } else if (data.status === ORDER_STATUS.COMPLETED) {
        // 如果是已完成状态但没有回收订单号，生成一个临时的
        recycleOrderNo.value = `RC${data.order_no.substring(2)}`
      }

    } else {
      throw new Error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: error.message || '加载失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时获取参数
onLoad((options) => {
  console.log('页面参数:', options)
  pageParams.value = options

  if (options.id) {
    loadOrderDetail(options.id)
  } else {
    uni.showToast({
      title: '缺少订单ID参数',
      icon: 'none'
    })
  }
})

// 页面挂载时的初始化
onMounted(() => {
  console.log('页面挂载完成')
  // 初始化日期描述
  initDateDescriptions()
})
</script>

<style lang="scss" scoped>
.quote-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding-top: 20rpx;
  padding-bottom: 120rpx;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid rgba(22, 160, 133, 0.2);
      border-top: 4rpx solid #16a085;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 商品信息卡片（融合设计）
.product-card {
  background-color: #fff;
  margin: 24rpx 32rpx 20rpx 32rpx;
  padding: 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  overflow: hidden;

  // 待发货状态的顶部提示栏
  .shipping-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    padding: 20rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 193, 7, 0.3) 50%, transparent 100%);
    }

    .notice-text {
      font-size: 26rpx;
      color: #856404;
      font-weight: 500;
      flex: 1;
      line-height: 1.5;
      margin-right: 16rpx;
    }

    .notice-right {
      font-size: 22rpx;
      color: #856404;
      font-weight: 600;
      background: rgba(255, 255, 255, 0.9);
      padding: 6rpx 16rpx;
      border-radius: 16rpx;
      border: 1rpx solid rgba(255, 193, 7, 0.4);
      box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.2);
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 24rpx;
    padding: 32rpx 24rpx 20rpx 24rpx;
    border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      background-color: #f0f0f0;

      &.no-image {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #ddd;
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-code {
        font-size: 24rpx;
        color: #999;
      }

      .order-no {
        font-size: 24rpx;
        color: #16a085;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      }
    }
  }

  .status-section {
    padding: 0 24rpx;

    .status-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .status-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;

        &.quoting {
          background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
          color: #856404;
          border: 1rpx solid #ffeaa7;
        }

        &.pending-confirm {
          background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
          color: #0c5460;
          border: 1rpx solid #bee5eb;
        }

        &.pending-ship {
          background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
          color: #155724;
          border: 1rpx solid #c3e6cb;
        }

        &.completed {
          background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
          color: #383d41;
          border: 1rpx solid #d6d8db;
        }

        &.cancelled {
          background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
          color: #721c24;
          border: 1rpx solid #f5c6cb;
        }
      }
    }

    .price-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 600;
        }

        .price-value {
          font-size: 36rpx;
          color: #16a085;
          font-weight: 700;
        }
      }

      .price-pending {
        font-size: 28rpx;
        color: #999;
        font-weight: 500;
        font-style: italic;
      }
    }
  }

  // 时间信息区域
  .time-info {
    margin-bottom: 24rpx;
    padding: 0 24rpx 20rpx 24rpx;
    border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

    .time-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8rpx 0;

      .time-label {
        font-size: 24rpx;
        color: #666;
        font-weight: 500;
      }

      .time-value {
        font-size: 24rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }

  // 用户上传的图片（融合在商品卡片中）
  .upload-photos {
    padding: 0 24rpx 24rpx 24rpx;

    .photos-title {
      font-size: 26rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 16rpx;
      display: block;
    }

    .photos-scroll {
      white-space: nowrap;

      .photos-list {
        display: flex;
        gap: 16rpx;
        padding-bottom: 8rpx;

        .photo-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8rpx;

          .photo-image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 12rpx;
            background-color: #f0f0f0;
            border: 2rpx solid rgba(22, 160, 133, 0.1);
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
            }
          }

          .photo-label {
            font-size: 20rpx;
            color: #666;
            text-align: center;
            max-width: 100rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// 通用卡片样式
.accessories-card,
.quote-note-card,
.user-note-card {
  background-color: #fff;
  margin: 0 32rpx 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .card-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 20rpx;
    display: block;
  }
}

// 配件列表（标签式并排布局）
.accessories-card {
  .accessories-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .accessory-tag {
      background: linear-gradient(135deg, #f8fffe 0%, #f0f9f8 100%);
      border: 1rpx solid rgba(22, 160, 133, 0.2);
      border-radius: 20rpx;
      padding: 12rpx 20rpx;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #e8f5f3 0%, #d4edda 100%);
        border-color: rgba(22, 160, 133, 0.4);
      }

      .accessory-name {
        font-size: 26rpx;
        color: #16a085;
        font-weight: 500;
        line-height: 1.2;
      }
    }
  }
}

// 估价说明（特殊样式）
.quote-note-card {
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  border: 1rpx solid rgba(255, 193, 7, 0.3);

  .card-title {
    color: #856404;
    position: relative;

    &::before {
      content: '💰';
      margin-right: 8rpx;
    }
  }

  .note-content {
    .note-text {
      font-size: 26rpx;
      color: #856404;
      line-height: 1.6;
      word-break: break-all;
    }
  }
}

// 用户备注
.user-note-card {
  .note-content {
    .note-text {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      word-break: break-all;
    }
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);

  // 单个按钮布局
  .single-button {
    width: 100%;

    .cancel-button, .recycle-button {
      width: 100%;
      padding: 24rpx 0;
      border-radius: 32rpx;
      text-align: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .button-text {
        font-size: 28rpx;
        font-weight: 600;
        letter-spacing: 1rpx;
      }
    }

    .cancel-button {
      background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
      border: 2rpx solid #dc3545;
      box-shadow: 0 2rpx 8rpx rgba(220, 53, 69, 0.15);

      .button-text {
        color: #dc3545;
      }

      &:active {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }
    }

    .recycle-button {
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      border: 2rpx solid #16a085;
      box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.25);

      .button-text {
        color: #fff;
      }

      &:active {
        background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);
      }
    }
  }

  // 双按钮布局
  .double-buttons {
    display: flex;
    gap: 16rpx;

    .cancel-button, .confirm-button {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 32rpx;
      text-align: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .button-text {
        font-size: 28rpx;
        font-weight: 600;
        letter-spacing: 1rpx;
      }
    }

    .cancel-button.secondary {
      background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
      border: 2rpx solid #6c757d;
      box-shadow: 0 2rpx 8rpx rgba(108, 117, 125, 0.15);

      .button-text {
        color: #6c757d;
      }

      &:active {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }
    }

    .confirm-button.primary {
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      border: 2rpx solid #16a085;
      box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.25);

      .button-text {
        color: #fff;
      }

      &:active {
        background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);
      }
    }
  }
}

// 已完成状态的成功展示区域
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;

  .success-icon {
    position: relative;
    margin-bottom: 32rpx;

    .clipboard-bg {
      position: relative;
      width: 120rpx;
      height: 140rpx;

      .clipboard-body {
        width: 100%;
        height: 120rpx;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        border-radius: 12rpx;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(44, 62, 80, 0.3);

        .clipboard-lines {
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .line {
            height: 4rpx;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 2rpx;

            &:nth-child(1) {
              width: 60rpx;
            }

            &:nth-child(2) {
              width: 48rpx;
            }

            &:nth-child(3) {
              width: 36rpx;
            }
          }
        }
      }

      .clipboard-clip {
        position: absolute;
        top: -8rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 16rpx;
        background-color: #fff;
        border-radius: 4rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }
    }

    .check-mark {
      position: absolute;
      bottom: -8rpx;
      right: -8rpx;
      width: 40rpx;
      height: 40rpx;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(13, 115, 119, 0.5);
      border: 3rpx solid #fff;

      .check-icon {
        font-size: 18rpx;
        color: #fff;
        font-weight: bold;
      }
    }
  }

  .success-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .order-number {
    font-size: 26rpx;
    color: #16a085;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .success-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    max-width: 500rpx;
    margin-bottom: 32rpx;
  }

  // 查看订单按钮
  .view-order-button {
    padding: 16rpx 40rpx;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    border: none;
    border-radius: 32rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 20rpx rgba(13, 115, 119, 0.4);

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #0a5d61 0%, #117a65 100%);
      box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.5);
    }

    .button-text {
      letter-spacing: 1rpx;
      color: #fff;
    }
  }
}

// 待发货状态样式
.shipping-section {
  margin: 20rpx 0;



  // 配送方式
  .delivery-section {
    background-color: #fff;
    margin: 20rpx 24rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .delivery-options {
      display: flex;
      background-color: #f5f5f5;
      border-radius: 12rpx;
      padding: 8rpx;
      margin: 24rpx;

      .delivery-option {
        flex: 1;
        position: relative;
        padding: 20rpx 24rpx;
        background-color: transparent;
        border-radius: 8rpx;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;

        &:active {
          transform: scale(0.98);
        }

        &.active {
          background-color: #fff;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

          .option-name {
            color: #333;
            font-weight: 600;
          }

          .option-tag {
            .tag-text {
              background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
              color: #fff;
              box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.3);
            }
          }
        }

        .option-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;

          .option-name {
            font-size: 26rpx;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
          }

          .option-tag {
            .tag-text {
              font-size: 20rpx;
              color: #fff;
              background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
              padding: 4rpx 12rpx;
              border-radius: 12rpx;
              font-weight: 600;
              transition: all 0.3s ease;
              box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.3);
            }
          }
        }
      }
    }

    .pickup-content, .self-content {
      .address-item, .time-item, .express-item, .tracking-item {
        display: flex;
        align-items: center;
        padding: 32rpx 24rpx;
        border-bottom: 1rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active:not(.disabled) {
          background-color: #f8f9fa;
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .address-icon, .time-icon, .express-icon, .tracking-icon {
          margin-right: 20rpx;
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 32rpx;
            height: 32rpx;
          }

          &.orange-bg {
            background-color: #16a085;
            border-radius: 50%;

            .address-text-icon {
              color: #fff;
              font-size: 24rpx;
              font-weight: 600;
            }
          }
        }

        .address-text, .time-text, .express-text, .tracking-text {
          flex: 1;
          font-size: 30rpx;
          color: #212529;
          font-weight: 500;
        }

        .address-action, .time-action, .express-action {
          font-size: 26rpx;
          color: #6c757d;
          font-weight: 500;

          &.disabled {
            color: #999;
          }
        }

        .tracking-input {
          flex: 1;
          font-size: 26rpx;
          color: #333;
          text-align: right;
          background: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: #999;
          }
        }

        .address-content {
          flex: 1;
          margin-right: 20rpx;

          .selected-address {
            .address-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 600;
              margin-bottom: 8rpx;
              display: block;
            }

            .address-detail {
              font-size: 24rpx;
              color: #666;
              line-height: 1.4;
              display: block;
            }
          }
        }

        .divider-line {
          width: 1rpx;
          height: 32rpx;
          background-color: #e9ecef;
          margin: 0 16rpx;
        }

        .address-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .address-name {
            font-size: 30rpx;
            color: #212529;
            font-weight: 500;
          }

          .address-detail {
            font-size: 24rpx;
            color: #6c757d;
            line-height: 1.4;
          }
        }

        .copy-btn {
          background-color: #16a085;
          color: #fff;
          padding: 12rpx 24rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 500;
        }
      }
    }
  }
}

// 快递公司选择弹窗
.express-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .modal-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .express-list {
      max-height: 400rpx;
      overflow-y: auto;

      .express-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f8f9fa;
        transition: all 0.3s ease;

        &:active {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .express-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .express-check {
          color: #16a085;
          font-size: 24rpx;
          font-weight: 600;
        }
      }
    }
  }
}

// 时间选择弹窗样式
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .time-modal-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;

    .time-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .time-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .time-container {
      display: flex;
      min-height: 400rpx;

      // 左侧日期选择
      .date-sidebar {
        width: 200rpx;
        background-color: #f8f9fa;
        border-right: 1rpx solid #e9ecef;

        .date-option {
          padding: 32rpx 24rpx;
          border-bottom: 1rpx solid #e9ecef;
          text-align: center;
          transition: all 0.3s ease;
          cursor: pointer;

          &:active {
            background-color: #e9ecef;
          }

          &.active {
            background-color: #16a085;
            color: #fff;

            .date-text, .date-desc {
              color: #fff;
            }
          }

          .date-text {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
          }

          .date-desc {
            font-size: 22rpx;
            color: #666;
          }
        }
      }

      // 右侧时间选择
      .time-content {
        flex: 1;
        padding: 32rpx 24rpx;
        overflow-y: auto;

        .time-slots {
          display: flex;
          flex-direction: column;
          gap: 20rpx;

          .time-slot {
            padding: 24rpx 20rpx;
            border: 2rpx solid #e9ecef;
            border-radius: 12rpx;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;

            &:active {
              transform: scale(0.98);
            }

            &.active {
              border-color: #16a085;
              background-color: #f0fffe;

              .slot-title {
                color: #16a085;
                font-weight: 600;
              }

              .slot-desc {
                color: #16a085;
              }
            }

            &.urgent {
              border-color: #ff6b35;
              background: linear-gradient(135deg, #fff5f0 0%, #ffeee6 100%);

              &.active {
                border-color: #ff6b35;
                background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);

                .slot-title, .slot-desc {
                  color: #fff;
                }
              }

              .slot-title {
                color: #ff6b35;
                font-weight: 600;
              }

              .slot-desc {
                color: #ff6b35;
              }
            }

            .slot-title {
              display: block;
              font-size: 28rpx;
              color: #333;
              margin-bottom: 8rpx;
            }

            .slot-desc {
              font-size: 22rpx;
              color: #666;
            }
          }
        }
      }
    }

    .time-confirm {
      padding: 24rpx 32rpx;
      border-top: 1rpx solid #f0f0f0;

      .confirm-btn {
        width: 100%;
        background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
        color: #fff;
        padding: 24rpx 0;
        border-radius: 32rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);
        }

        .confirm-text {
          color: #fff;
        }
      }
    }
  }
}

// 取消订单弹窗
.cancel-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .cancel-modal-content {
    background: #fff;
    margin: 0 40rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
    max-width: 600rpx;
    width: 100%;

    .cancel-header {
      padding: 32rpx 32rpx 0 32rpx;
      text-align: center;

      .cancel-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 700;
      }
    }

    .cancel-body {
      padding: 24rpx 32rpx 32rpx 32rpx;
      text-align: center;

      .cancel-message {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        margin-bottom: 16rpx;
        display: block;
      }

      .cancel-note {
        font-size: 24rpx;
        color: #999;
        line-height: 1.4;
        display: block;
      }
    }

    .cancel-actions {
      display: flex;
      border-top: 1rpx solid rgba(0, 0, 0, 0.1);

      .cancel-btn {
        flex: 1;
        padding: 24rpx 0;
        text-align: center;
        transition: all 0.3s ease;

        &.secondary {
          background: #fff;
          border-right: 1rpx solid rgba(0, 0, 0, 0.1);

          &:active {
            background: #f8f9fa;
          }

          .btn-text {
            color: #666;
            font-size: 28rpx;
            font-weight: 500;
          }
        }

        &.primary {
          background: #dc3545;

          &:active {
            background: #c82333;
          }

          .btn-text {
            color: #fff;
            font-size: 28rpx;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>