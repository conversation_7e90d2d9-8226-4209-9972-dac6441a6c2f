<template>
  <view class="quote-detail-page">
    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content" v-else>
      <!-- 商品信息卡片 -->
      <view class="product-card">
      <view class="product-info">
        <image
          v-if="orderInfo.product_image"
          :src="img(orderInfo.product_image)"
          class="product-image"
          mode="aspectFit"
        ></image>
        <view
          v-else
          class="product-image no-image"
        >
          <u-icon name="image" color="#ccc" size="40"></u-icon>
        </view>
        <view class="product-details">
          <text class="product-name">{{ orderInfo.product_name || '商品名称' }}</text>
          <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
        </view>
      </view>

      <!-- 订单状态显示 -->
      <view class="status-section">
        <view class="status-info">
          <text class="status-label">订单状态</text>
          <view class="status-badge" :class="getStatusClass(orderInfo.status)">
            <text class="status-text">{{ getStatusText(orderInfo.status) }}</text>
          </view>
        </view>

        <!-- 价格信息 -->
        <view class="price-info" v-if="orderInfo.quote_price">
          <text class="price-label">估价金额</text>
          <view class="price-amount">
            <text class="currency">¥</text>
            <text class="price-value">{{ orderInfo.quote_price }}</text>
          </view>
        </view>
        <view class="price-info" v-else>
          <text class="price-label">估价金额</text>
          <text class="price-pending">估价中...</text>
        </view>
      </view>
    </view>

    <!-- 用户上传的图片 -->
    <view class="upload-photos-card" v-if="userPhotos.length > 0">
      <text class="card-title">评估图片</text>
      <scroll-view class="photos-scroll" scroll-x="true" show-scrollbar="false">
        <view class="photos-list">
          <view
            class="photo-item"
            v-for="photo in userPhotos"
            :key="photo.id"
          >
            <image
              :src="img(photo.photo_url)"
              class="photo-image"
              mode="aspectFill"
              @click="previewPhoto(photo.photo_url)"
            ></image>
            <text class="photo-label">{{ photo.photo_name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 选择的配件 -->
    <view class="accessories-card" v-if="orderAccessories.length > 0">
      <text class="card-title">选择配件</text>
      <view class="accessories-list">
        <view
          class="accessory-item"
          v-for="accessory in orderAccessories"
          :key="accessory.id"
        >
          <view class="accessory-info">
            <text class="accessory-name">{{ accessory.accessory_name }}</text>
            <text class="accessory-status" :class="accessory.has_accessory ? 'has' : 'missing'">
              {{ accessory.has_accessory ? '有' : '无' }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户备注 -->
    <view class="user-note-card" v-if="orderInfo.user_note">
      <text class="card-title">用户备注</text>
      <view class="note-content">
        <text class="note-text">{{ orderInfo.user_note }}</text>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <text class="card-title">订单信息</text>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ orderInfo.order_no }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ formatTime(orderInfo.create_time) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.quote_time">
          <text class="info-label">估价时间</text>
          <text class="info-value">{{ formatTime(orderInfo.quote_time) }}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 - 仅在估价中状态显示 -->
    <view class="bottom-actions" v-if="orderInfo.status === 1">
      <view class="cancel-button" @click="showCancelDialog">
        <text class="button-text">取消订单</text>
      </view>
    </view>

    <!-- 取消订单确认弹窗 -->
    <view class="cancel-modal" v-if="showCancel" @click="hideCancelDialog">
      <view class="cancel-modal-content" @click.stop>
        <view class="cancel-header">
          <text class="cancel-title">取消订单</text>
        </view>
        <view class="cancel-body">
          <text class="cancel-message">确定要取消这个估价订单吗？</text>
          <text class="cancel-note">取消后将无法恢复，需要重新提交估价申请。</text>
        </view>
        <view class="cancel-actions">
          <view class="cancel-btn secondary" @click="hideCancelDialog">
            <text class="btn-text">再想想</text>
          </view>
          <view class="cancel-btn primary" @click="confirmCancel">
            <text class="btn-text">确认取消</text>
          </view>
        </view>
      </view>
    </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderDetail, cancelQuoteOrder } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 订单信息
const orderInfo = ref<any>({
  id: '',
  order_no: '',
  product_name: '',
  product_code: '',
  product_image: '',
  status: 1,
  quote_price: null,
  user_note: '',
  create_time: '',
  quote_time: ''
})

// 用户上传的图片
const userPhotos = ref<any[]>([])

// 订单配件
const orderAccessories = ref<any[]>([])

// 加载状态
const loading = ref(false)

// 取消订单弹窗
const showCancel = ref(false)
const cancelling = ref(false)

// 订单状态常量
const ORDER_STATUS = {
  QUOTING: 1,        // 估价中
  PENDING_CONFIRM: 2, // 待确认
  PENDING_SHIP: 3,   // 待发货
  COMPLETED: 4,      // 已完成
  CANCELLED: 5       // 已取消
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    [ORDER_STATUS.QUOTING]: '估价中',
    [ORDER_STATUS.PENDING_CONFIRM]: '待确认',
    [ORDER_STATUS.PENDING_SHIP]: '待发货',
    [ORDER_STATUS.COMPLETED]: '已完成',
    [ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  const classMap = {
    [ORDER_STATUS.QUOTING]: 'quoting',
    [ORDER_STATUS.PENDING_CONFIRM]: 'pending-confirm',
    [ORDER_STATUS.PENDING_SHIP]: 'pending-ship',
    [ORDER_STATUS.COMPLETED]: 'completed',
    [ORDER_STATUS.CANCELLED]: 'cancelled'
  }
  return classMap[status] || 'default'
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 预览图片
const previewPhoto = (photoUrl: string) => {
  const allPhotos = userPhotos.value.map(photo => img(photo.photo_url))
  const currentIndex = allPhotos.indexOf(img(photoUrl))

  uni.previewImage({
    urls: allPhotos,
    current: currentIndex
  })
}

// 显示取消订单弹窗
const showCancelDialog = () => {
  showCancel.value = true
}

// 隐藏取消订单弹窗
const hideCancelDialog = () => {
  showCancel.value = false
}

// 确认取消订单
const confirmCancel = async () => {
  if (cancelling.value) return

  try {
    cancelling.value = true

    await cancelQuoteOrder(orderInfo.value.id, '用户主动取消')

    uni.showToast({
      title: '订单已取消',
      icon: 'success',
      duration: 2000
    })

    // 更新订单状态
    orderInfo.value.status = ORDER_STATUS.CANCELLED

    // 关闭弹窗
    showCancel.value = false

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)

  } catch (error) {
    console.error('取消订单失败:', error)
    uni.showToast({
      title: error.message || '取消失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    cancelling.value = false
  }
}

// 加载订单详情
const loadOrderDetail = async (orderId: string) => {
  if (!orderId) {
    uni.showToast({
      title: '订单ID不能为空',
      icon: 'none'
    })
    return
  }

  try {
    loading.value = true

    const response = await getQuoteOrderDetail(parseInt(orderId))

    if (response.code === 1) {
      const data = response.data

      // 设置订单基本信息
      orderInfo.value = {
        id: data.id,
        order_no: data.order_no || '',
        product_name: data.product_name || data.brand?.name || '估价商品',
        product_code: data.product_code || '',
        product_image: data.product_image || '',
        status: data.status || ORDER_STATUS.QUOTING,
        quote_price: data.quote_price || null,
        user_note: data.user_note || '',
        create_time: data.create_time || '',
        quote_time: data.quote_time || ''
      }

      // 设置用户上传的图片
      userPhotos.value = (data.photos || []).map(photo => ({
        ...photo,
        photo_url: photo.photo_url
      }))

      // 设置订单配件
      orderAccessories.value = data.accessories || []

    } else {
      throw new Error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: error.message || '加载失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时获取参数
onLoad((options) => {
  console.log('页面参数:', options)
  pageParams.value = options

  if (options.id) {
    loadOrderDetail(options.id)
  } else {
    uni.showToast({
      title: '缺少订单ID参数',
      icon: 'none'
    })
  }
})
</script>

<style lang="scss" scoped>
.quote-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding-top: 20rpx;
  padding-bottom: 120rpx;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid rgba(22, 160, 133, 0.2);
      border-top: 4rpx solid #16a085;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 商品信息卡片
.product-card {
  background-color: #fff;
  margin: 24rpx 32rpx 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .product-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      background-color: #f0f0f0;

      &.no-image {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #ddd;
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-code {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .status-section {
    .status-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .status-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;

        &.quoting {
          background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
          color: #856404;
          border: 1rpx solid #ffeaa7;
        }

        &.pending-confirm {
          background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
          color: #0c5460;
          border: 1rpx solid #bee5eb;
        }

        &.pending-ship {
          background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
          color: #155724;
          border: 1rpx solid #c3e6cb;
        }

        &.completed {
          background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
          color: #383d41;
          border: 1rpx solid #d6d8db;
        }

        &.cancelled {
          background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
          color: #721c24;
          border: 1rpx solid #f5c6cb;
        }
      }
    }

    .price-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 600;
        }

        .price-value {
          font-size: 36rpx;
          color: #16a085;
          font-weight: 700;
        }
      }

      .price-pending {
        font-size: 28rpx;
        color: #999;
        font-weight: 500;
        font-style: italic;
      }
    }
  }
}

// 通用卡片样式
.upload-photos-card,
.accessories-card,
.user-note-card,
.order-info-card {
  background-color: #fff;
  margin: 0 32rpx 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .card-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 20rpx;
    display: block;
  }
}

// 用户上传的图片
.upload-photos-card {
  .photos-scroll {
    white-space: nowrap;

    .photos-list {
      display: flex;
      gap: 16rpx;
      padding-bottom: 8rpx;

      .photo-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;

        .photo-image {
          width: 100rpx;
          height: 100rpx;
          border-radius: 12rpx;
          background-color: #f0f0f0;
          border: 2rpx solid rgba(22, 160, 133, 0.1);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
          }
        }

        .photo-label {
          font-size: 20rpx;
          color: #666;
          text-align: center;
          max-width: 100rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// 配件列表
.accessories-card {
  .accessories-list {
    .accessory-item {
      padding: 16rpx 0;
      border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .accessory-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .accessory-name {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .accessory-status {
          font-size: 24rpx;
          font-weight: 600;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;

          &.has {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
          }

          &.missing {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
          }
        }
      }
    }
  }
}

// 用户备注
.user-note-card {
  .note-content {
    .note-text {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      word-break: break-all;
    }
  }
}

// 订单信息
.order-info-card {
  .info-list {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .info-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        text-align: right;
        max-width: 400rpx;
        word-break: break-all;
      }
    }
  }
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);

  .cancel-button {
    width: 100%;
    padding: 24rpx 0;
    background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
    border: 2rpx solid #dc3545;
    border-radius: 32rpx;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(220, 53, 69, 0.15);

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .button-text {
      font-size: 28rpx;
      color: #dc3545;
      font-weight: 600;
      letter-spacing: 1rpx;
    }
  }
}

// 取消订单弹窗
.cancel-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .cancel-modal-content {
    background: #fff;
    margin: 0 40rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
    max-width: 600rpx;
    width: 100%;

    .cancel-header {
      padding: 32rpx 32rpx 0 32rpx;
      text-align: center;

      .cancel-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 700;
      }
    }

    .cancel-body {
      padding: 24rpx 32rpx 32rpx 32rpx;
      text-align: center;

      .cancel-message {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        margin-bottom: 16rpx;
        display: block;
      }

      .cancel-note {
        font-size: 24rpx;
        color: #999;
        line-height: 1.4;
        display: block;
      }
    }

    .cancel-actions {
      display: flex;
      border-top: 1rpx solid rgba(0, 0, 0, 0.1);

      .cancel-btn {
        flex: 1;
        padding: 24rpx 0;
        text-align: center;
        transition: all 0.3s ease;

        &.secondary {
          background: #fff;
          border-right: 1rpx solid rgba(0, 0, 0, 0.1);

          &:active {
            background: #f8f9fa;
          }

          .btn-text {
            color: #666;
            font-size: 28rpx;
            font-weight: 500;
          }
        }

        &.primary {
          background: #dc3545;

          &:active {
            background: #c82333;
          }

          .btn-text {
            color: #fff;
            font-size: 28rpx;
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>