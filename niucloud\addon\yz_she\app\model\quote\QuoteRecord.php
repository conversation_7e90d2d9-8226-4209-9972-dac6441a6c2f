<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\quote;

use core\base\BaseModel;

/**
 * 估价记录模型
 * Class QuoteRecord
 * @package addon\yz_she\app\model\quote
 */
class QuoteRecord extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_quote_records';

    /**
     * 关联估价订单
     * @return \think\model\relation\BelongsTo
     */
    public function quoteOrder()
    {
        return $this->belongsTo(QuoteOrder::class, 'quote_order_id', 'id');
    }

    /**
     * 搜索器：估价订单ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchQuoteOrderIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('quote_order_id', $value);
        }
    }

    /**
     * 搜索器：管理员ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchAdminIdAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('admin_id', $value);
        }
    }
}
